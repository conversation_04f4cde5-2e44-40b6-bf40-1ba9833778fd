# GitLab CI/CD Pipeline for Open WebUI
# 完整的 Docker 容器化 CI/CD 流程

variables:
  # Docker 相關變量
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"
  
  # 鏡像名稱和標籤
  IMAGE_NAME: "${CI_REGISTRY_IMAGE}"
  IMAGE_TAG: "${CI_COMMIT_REF_SLUG}-${CI_COMMIT_SHORT_SHA}"
  LATEST_TAG: "${CI_COMMIT_REF_SLUG}-latest"
  
  # 構建參數
  BUILD_HASH: "${CI_COMMIT_SHA}"
  
  # 部署相關
  COMPOSE_PROJECT_NAME: "open-webui-${CI_ENVIRONMENT_SLUG}"

# 定義階段
stages:
  - test
  - build
  - security
  - deploy
  - cleanup

# 通用模板
.docker_template: &docker_template
  image: docker:24.0.5
  services:
    - docker:24.0.5-dind
  before_script:
    - echo $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY

.deploy_template: &deploy_template
  image: alpine:latest
  before_script:
    - apk add --no-cache docker-compose curl jq openssh-client
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan -H $DEPLOY_HOST >> ~/.ssh/known_hosts

# ==================== 測試階段 ====================
lint-frontend:
  stage: test
  image: node:22-alpine3.20
  cache:
    key: npm-cache
    paths:
      - node_modules/
  script:
    - npm ci --force
    - npm run lint:frontend
  only:
    - merge_requests
    - main
    - develop
    - staging

lint-backend:
  stage: test
  image: python:3.11-slim
  cache:
    key: pip-cache
    paths:
      - .cache/pip
  before_script:
    - pip install --cache-dir .cache/pip pylint black
  script:
    - cd backend
    - pylint open_webui/
    - black --check .
  only:
    - merge_requests
    - main
    - develop
    - staging

test-frontend:
  stage: test
  image: node:22-alpine3.20
  cache:
    key: npm-cache
    paths:
      - node_modules/
  script:
    - npm ci --force
    - npm run test:frontend
  only:
    - merge_requests
    - main
    - develop
    - staging

# ==================== 構建階段 ====================
build-main-image:
  stage: build
  <<: *docker_template
  script:
    # 構建主要鏡像（CPU 版本）
    - docker build 
        --build-arg BUILD_HASH=$BUILD_HASH
        --build-arg USE_CUDA=false
        --build-arg USE_OLLAMA=false
        -t $IMAGE_NAME:$IMAGE_TAG
        -t $IMAGE_NAME:$LATEST_TAG
        .
    - docker push $IMAGE_NAME:$IMAGE_TAG
    - docker push $IMAGE_NAME:$LATEST_TAG
    # 保存鏡像信息供後續使用
    - echo "IMAGE_NAME=$IMAGE_NAME" > build.env
    - echo "IMAGE_TAG=$IMAGE_TAG" >> build.env
    - echo "LATEST_TAG=$LATEST_TAG" >> build.env
  artifacts:
    reports:
      dotenv: build.env
    expire_in: 1 hour
  only:
    - main
    - develop
    - staging

build-cuda-image:
  stage: build
  <<: *docker_template
  script:
    # 構建 CUDA 版本鏡像
    - docker build 
        --build-arg BUILD_HASH=$BUILD_HASH
        --build-arg USE_CUDA=true
        --build-arg USE_CUDA_VER=cu128
        --build-arg USE_OLLAMA=false
        -t $IMAGE_NAME:$IMAGE_TAG-cuda
        -t $IMAGE_NAME:$LATEST_TAG-cuda
        .
    - docker push $IMAGE_NAME:$IMAGE_TAG-cuda
    - docker push $IMAGE_NAME:$LATEST_TAG-cuda
  only:
    - main
    - develop
    - staging
  when: manual  # 手動觸發，因為 CUDA 構建時間較長

build-ollama-image:
  stage: build
  <<: *docker_template
  script:
    # 構建包含 Ollama 的鏡像
    - docker build 
        --build-arg BUILD_HASH=$BUILD_HASH
        --build-arg USE_CUDA=false
        --build-arg USE_OLLAMA=true
        -t $IMAGE_NAME:$IMAGE_TAG-ollama
        -t $IMAGE_NAME:$LATEST_TAG-ollama
        .
    - docker push $IMAGE_NAME:$IMAGE_TAG-ollama
    - docker push $IMAGE_NAME:$LATEST_TAG-ollama
  only:
    - main
    - develop
    - staging
  when: manual  # 手動觸發

# ==================== 安全掃描階段 ====================
container-security-scan:
  stage: security
  image: aquasec/trivy:latest
  script:
    - trivy image --exit-code 0 --severity HIGH,CRITICAL $IMAGE_NAME:$IMAGE_TAG
  dependencies:
    - build-main-image
  only:
    - main
    - develop
    - staging
  allow_failure: true  # 允許失敗，不阻塞部署

# ==================== 部署階段 ====================
deploy-staging:
  stage: deploy
  <<: *deploy_template
  environment:
    name: staging
    url: https://staging.your-domain.com
  variables:
    DEPLOY_HOST: $STAGING_HOST
    DEPLOY_USER: $STAGING_USER
    COMPOSE_FILE: "docker-compose.yaml"
  script:
    - |
      # 創建部署腳本
      cat > deploy.sh << 'EOF'
      #!/bin/bash
      set -e
      
      # 設置環境變量
      export IMAGE_NAME=$1
      export IMAGE_TAG=$2
      export COMPOSE_PROJECT_NAME=$3
      export ENVIRONMENT=staging
      
      # 創建 docker-compose.override.yml
      cat > docker-compose.override.yml << 'COMPOSE_EOF'
      services:
        open-webui:
          image: ${IMAGE_NAME}:${IMAGE_TAG}
          environment:
            - ENV=staging
            - WEBUI_SECRET_KEY=${WEBUI_SECRET_KEY}
            - OPENAI_API_KEY=${OPENAI_API_KEY}
          labels:
            - "traefik.enable=true"
            - "traefik.http.routers.open-webui-staging.rule=Host(\`staging.your-domain.com\`)"
            - "traefik.http.routers.open-webui-staging.tls=true"
            - "traefik.http.routers.open-webui-staging.tls.certresolver=letsencrypt"
      COMPOSE_EOF
      
      # 拉取最新鏡像
      docker-compose pull open-webui
      
      # 停止舊容器並啟動新容器
      docker-compose down
      docker-compose up -d
      
      # 等待服務啟動
      echo "等待服務啟動..."
      sleep 30
      
      # 健康檢查
      if curl -f http://localhost:3000/health; then
        echo "部署成功！"
      else
        echo "健康檢查失敗，回滾..."
        docker-compose down
        exit 1
      fi
      
      # 清理舊鏡像
      docker image prune -f
      EOF
      
      chmod +x deploy.sh
      
      # 上傳文件到服務器
      scp docker-compose.yaml $DEPLOY_USER@$DEPLOY_HOST:/opt/open-webui/
      scp deploy.sh $DEPLOY_USER@$DEPLOY_HOST:/opt/open-webui/
      
      # 執行部署
      ssh $DEPLOY_USER@$DEPLOY_HOST "cd /opt/open-webui && ./deploy.sh $IMAGE_NAME $IMAGE_TAG $COMPOSE_PROJECT_NAME"
  dependencies:
    - build-main-image
  only:
    - staging
  when: manual

deploy-production:
  stage: deploy
  <<: *deploy_template
  environment:
    name: production
    url: https://your-domain.com
  variables:
    DEPLOY_HOST: $PRODUCTION_HOST
    DEPLOY_USER: $PRODUCTION_USER
    COMPOSE_FILE: "docker-compose.yaml"
  script:
    - |
      # 創建生產環境部署腳本
      cat > deploy-prod.sh << 'EOF'
      #!/bin/bash
      set -e
      
      # 設置環境變量
      export IMAGE_NAME=$1
      export IMAGE_TAG=$2
      export COMPOSE_PROJECT_NAME=$3
      export ENVIRONMENT=production
      
      # 創建生產環境 docker-compose.override.yml
      cat > docker-compose.override.yml << 'COMPOSE_EOF'
      services:
        open-webui:
          image: ${IMAGE_NAME}:${IMAGE_TAG}
          restart: unless-stopped
          environment:
            - ENV=production
            - WEBUI_SECRET_KEY=${WEBUI_SECRET_KEY}
            - OPENAI_API_KEY=${OPENAI_API_KEY}
            - SCARF_NO_ANALYTICS=true
            - DO_NOT_TRACK=true
            - ANONYMIZED_TELEMETRY=false
          labels:
            - "traefik.enable=true"
            - "traefik.http.routers.open-webui.rule=Host(\`your-domain.com\`)"
            - "traefik.http.routers.open-webui.tls=true"
            - "traefik.http.routers.open-webui.tls.certresolver=letsencrypt"
          deploy:
            resources:
              limits:
                memory: 4G
              reservations:
                memory: 2G
      COMPOSE_EOF
      
      # 備份當前版本
      docker tag $(docker-compose images -q open-webui) $IMAGE_NAME:backup-$(date +%Y%m%d-%H%M%S) || true
      
      # 拉取新鏡像
      docker-compose pull open-webui
      
      # 滾動更新
      docker-compose up -d --no-deps open-webui
      
      # 等待服務啟動
      echo "等待服務啟動..."
      sleep 60
      
      # 健康檢查
      for i in {1..10}; do
        if curl -f http://localhost:3000/health; then
          echo "部署成功！"
          break
        else
          echo "健康檢查失敗，嘗試 $i/10..."
          sleep 10
          if [ $i -eq 10 ]; then
            echo "健康檢查最終失敗，回滾..."
            docker-compose down
            docker tag $IMAGE_NAME:backup-$(date +%Y%m%d)* $IMAGE_NAME:$IMAGE_TAG
            docker-compose up -d
            exit 1
          fi
        fi
      done
      
      # 清理舊鏡像（保留最近3個版本）
      docker images $IMAGE_NAME --format "table {{.Repository}}:{{.Tag}}\t{{.CreatedAt}}" | grep -v "backup\|latest" | tail -n +4 | awk '{print $1}' | xargs -r docker rmi || true
      EOF
      
      chmod +x deploy-prod.sh
      
      # 上傳文件到生產服務器
      scp docker-compose.yaml $DEPLOY_USER@$DEPLOY_HOST:/opt/open-webui/
      scp deploy-prod.sh $DEPLOY_USER@$DEPLOY_HOST:/opt/open-webui/
      
      # 執行生產部署
      ssh $DEPLOY_USER@$DEPLOY_HOST "cd /opt/open-webui && ./deploy-prod.sh $IMAGE_NAME $IMAGE_TAG $COMPOSE_PROJECT_NAME"
  dependencies:
    - build-main-image
  only:
    - main
  when: manual

# ==================== 清理階段 ====================
cleanup-registry:
  stage: cleanup
  image: alpine:latest
  before_script:
    - apk add --no-cache curl jq
  script:
    - |
      # 清理舊的鏡像標籤（保留最近10個版本）
      echo "清理 GitLab Container Registry 中的舊鏡像..."
      # 這裡可以添加清理邏輯，根據您的需求調整
  only:
    - main
  when: manual
  allow_failure: true

# ==================== 通知階段 ====================
notify-success:
  stage: .post
  image: alpine:latest
  before_script:
    - apk add --no-cache curl
  script:
    - |
      if [ "$CI_JOB_STATUS" == "success" ]; then
        # 發送成功通知到 Slack/Discord/Email
        curl -X POST -H 'Content-type: application/json' \
          --data "{\"text\":\"✅ Open WebUI 部署成功！\\n分支: $CI_COMMIT_REF_NAME\\n提交: $CI_COMMIT_SHORT_SHA\\n環境: $CI_ENVIRONMENT_NAME\"}" \
          $SLACK_WEBHOOK_URL || true
      fi
  when: on_success
  allow_failure: true

notify-failure:
  stage: .post
  image: alpine:latest
  before_script:
    - apk add --no-cache curl
  script:
    - |
      # 發送失敗通知
      curl -X POST -H 'Content-type: application/json' \
        --data "{\"text\":\"❌ Open WebUI 部署失敗！\\n分支: $CI_COMMIT_REF_NAME\\n提交: $CI_COMMIT_SHORT_SHA\\n流水線: $CI_PIPELINE_URL\"}" \
        $SLACK_WEBHOOK_URL || true
  when: on_failure
  allow_failure: true