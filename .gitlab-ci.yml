stages:
  - deploy
  
.runners:
  # 指定runner名
  tags: 
    - ai-w2

deploy-docker-dev:
  stage: deploy
  extends:
    - .runners
  # 在哪个分支上可用
  rules:
    - if: $CI_COMMIT_REF_NAME == "development"
  script:
    - echo "🚀 Starting Docker deployment for Open-WebUI..."
    - IMAGE_NAME="open-webui-dev"
    - CONTAINER_NAME="open-webui-dev"
    - HOST_PORT=3001 # 使用一个不同的端口，避免与生产环境冲突
    - CONTAINER_PORT=8080

    - echo "1. Building Docker image"
    - docker build --build-arg USE_CUDA=true -t "$IMAGE_NAME" .

    - echo "2. Stopping and removing old container"
    - docker stop "$CONTAINER_NAME" &>/dev/null || true
    - docker rm "$CONTAINER_NAME" &>/dev/null || true

    - echo "3. Running new container..."
    - |
      docker run -d -p "$HOST_PORT:$CONTAINER_PORT" \
          --add-host=host.docker.internal:host-gateway \
          -v "/data/open-webui_dev/backend/data/:/app/backend/data" \
          -e ENV=dev \
          --name "$CONTAINER_NAME" \
          --restart always \
          "$IMAGE_NAME"

    - echo "4. Cleaning up dangling images..."
    - docker image prune -f

    - echo "✅ Deployment successful!"
    - echo "🌐 Application is available at http://<your-runner-ip>:$HOST_PORT"

deploy-docker-staging:
  stage: deploy
  extends:
    - .runners
  # 在哪个分支上可用
  rules:
    - if: $CI_COMMIT_REF_NAME == "staging"
  script:
    - echo "🚀 Starting Docker deployment for Open-WebUI..."
    - IMAGE_NAME="open-webui-staging"
    - CONTAINER_NAME="open-webui-staging"
    - HOST_PORT=3002 # 使用一个不同的端口，避免与生产环境冲突
    - CONTAINER_PORT=8080

    - echo "1. Building Docker image"
    - docker build --build-arg USE_CUDA=true -t "$IMAGE_NAME" .

    - echo "2. Stopping and removing old container"
    - docker stop "$CONTAINER_NAME" &>/dev/null || true
    - docker rm "$CONTAINER_NAME" &>/dev/null || true

    - echo "3. Running new container..."
    - |
      docker run -d -p "$HOST_PORT:$CONTAINER_PORT" \
          --add-host=host.docker.internal:host-gateway \
          -v "/data/open-webui_staging/backend/data/:/app/backend/data" \
          --name "$CONTAINER_NAME" \
          --restart always \
          "$IMAGE_NAME"

    - echo "4. Cleaning up dangling images..."
    - docker image prune -f

    - echo "✅ Deployment successful!"
    - echo "🌐 Application is available at http://<your-runner-ip>:$HOST_PORT"

deploy-docker-demo:
  stage: deploy
  extends:
    - .runners
  rules:
    - if: $CI_COMMIT_REF_NAME == "demo"
  script:
    - echo "🚀 Starting Docker deployment for Open-WebUI (Demo)..."
    - IMAGE_NAME="open-webui-demo"
    - CONTAINER_NAME="open-webui-demo"
    - HOST_PORT=3003 # 使用一个不同的端口，避免与测试环境冲突
    - CONTAINER_PORT=8080

    - echo "1. Building Docker image"
    - docker build --build-arg USE_CUDA=true -t "$IMAGE_NAME" .

    - echo "2. Stopping and removing old container"
    - docker stop "$CONTAINER_NAME" &>/dev/null || true
    - docker rm "$CONTAINER_NAME" &>/dev/null || true

    - echo "3. Running new container..."
    - |
      docker run -d -p "$HOST_PORT:$CONTAINER_PORT" \
          --add-host=host.docker.internal:host-gateway \
          -v "/data/open-webui_demo/backend/data/:/app/backend/data" \
          --name "$CONTAINER_NAME" \
          --restart always \
          "$IMAGE_NAME"

    - echo "4. Cleaning up dangling images..."
    - docker image prune -f

    - echo "✅ Deployment successful!"
    - echo "🌐 Application is available at http://<your-runner-ip>:$HOST_PORT"
