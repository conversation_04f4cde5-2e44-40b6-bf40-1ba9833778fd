# Open WebUI 筆記資料夾功能實現

## 功能概述

本實現為 Open WebUI 添加了完整的筆記資料夾組織功能，包括：

- 📁 層級資料夾結構
- 🔄 筆記在資料夾間的移動
- 🎨 資料夾顏色自定義
- 🔍 資料夾內搜索
- 📊 筆記數量統計
- 🗂️ 面包屑導航

## 架構設計

### 後端架構

#### 數據庫設計

1. **新增 `note_folder` 表**：
   - `id`: 資料夾唯一標識
   - `user_id`: 用戶ID
   - `name`: 資料夾名稱
   - `description`: 資料夾描述
   - `color`: 資料夾顏色
   - `parent_id`: 父資料夾ID（支持層級結構）
   - `sort_order`: 排序順序
   - `access_control`: 權限控制
   - `created_at`, `updated_at`: 時間戳

2. **修改 `note` 表**：
   - 添加 `folder_id` 字段，關聯到 `note_folder.id`
   - 可為 NULL，表示筆記在根目錄

#### API 接口

**資料夾管理 API**：
- `GET /api/v1/notes/folders/` - 獲取所有資料夾
- `GET /api/v1/notes/folders/tree` - 獲取資料夾樹狀結構
- `POST /api/v1/notes/folders/` - 創建新資料夾
- `GET /api/v1/notes/folders/{id}` - 獲取指定資料夾
- `PUT /api/v1/notes/folders/{id}` - 更新資料夾
- `DELETE /api/v1/notes/folders/{id}` - 刪除資料夾
- `POST /api/v1/notes/folders/{id}/move` - 移動資料夾
- `GET /api/v1/notes/folders/{id}/path` - 獲取資料夾路徑

**筆記與資料夾關聯 API**：
- `POST /api/v1/notes/{id}/move` - 移動筆記到資料夾
- `GET /api/v1/notes/folders/{id}/notes` - 獲取資料夾中的筆記
- `GET /api/v1/notes/folders/root/notes` - 獲取根目錄筆記

### 前端架構

#### 組件結構

1. **FolderTree.svelte** - 資料夾樹狀結構組件
   - 展開/收縮資料夾
   - 資料夾操作（創建、編輯、刪除）
   - 拖拽支持（計劃中）

2. **NotesWithFolders.svelte** - 集成資料夾功能的筆記列表
   - 左側資料夾樹
   - 右側筆記列表
   - 面包屑導航
   - 搜索功能

3. **API 調用模塊** - `src/lib/apis/note-folders/index.ts`
   - 封裝所有資料夾相關的 API 調用

## 安裝和部署

### 1. 數據庫遷移

運行以下命令應用數據庫遷移：

```bash
cd backend
alembic upgrade head
```

### 2. 後端部署

確保以下文件已正確添加到項目中：
- `backend/open_webui/models/note_folders.py`
- `backend/open_webui/routers/note_folders.py`
- `backend/open_webui/migrations/versions/add_note_folders.py`

### 3. 前端部署

確保以下文件已正確添加：
- `src/lib/apis/note-folders/index.ts`
- `src/lib/components/notes/FolderTree.svelte`
- `src/lib/components/notes/NotesWithFolders.svelte`

修改 `src/routes/(app)/notes/+page.svelte` 使用新組件。

## 使用指南

### 創建資料夾

1. 在筆記頁面左側點擊 "+" 按鈕
2. 輸入資料夾名稱、描述（可選）
3. 選擇資料夾顏色
4. 點擊"創建"

### 管理資料夾

- **重命名**：點擊資料夾旁的編輯按鈕
- **刪除**：點擊資料夾旁的刪除按鈕（需要資料夾為空）
- **創建子資料夾**：點擊資料夾旁的 "+" 按鈕

### 組織筆記

- **移動筆記**：將來支持拖拽操作
- **在資料夾中創建筆記**：選中資料夾後點擊"新建筆記"
- **搜索**：在指定資料夾中搜索筆記

## 測試

### 運行測試腳本

1. 確保 Open WebUI 後端服務正在運行
2. 獲取有效的用戶 token
3. 修改 `test_note_folders.py` 中的 token
4. 運行測試：

```bash
python test_note_folders.py
```

### 手動測試

1. 訪問 `/notes` 頁面
2. 測試資料夾的創建、編輯、刪除
3. 測試筆記的創建和移動
4. 測試搜索功能

## 技術特性

### 性能優化

- 數據庫索引優化
- 樹狀結構查詢優化
- 前端組件懶加載

### 安全性

- 用戶權限驗證
- 資料夾訪問控制
- 防止循環引用

### 用戶體驗

- 響應式設計
- 拖拽操作（計劃中）
- 鍵盤快捷鍵（計劃中）

## 未來計劃

### 短期目標

- [ ] 實現拖拽功能
- [ ] 添加批量操作
- [ ] 改進搜索功能
- [ ] 添加資料夾共享功能

### 長期目標

- [ ] 資料夾模板
- [ ] 自動分類
- [ ] 標籤系統集成
- [ ] 導入/導出功能

## 故障排除

### 常見問題

1. **資料夾不顯示**
   - 檢查數據庫遷移是否成功
   - 確認用戶權限設置

2. **筆記移動失敗**
   - 檢查資料夾是否存在
   - 確認用戶有權限訪問目標資料夾

3. **前端組件錯誤**
   - 檢查 API 調用是否正確
   - 確認組件導入路徑

### 調試技巧

- 使用瀏覽器開發者工具查看網絡請求
- 檢查後端日誌文件
- 使用測試腳本驗證 API 功能

## 貢獻指南

歡迎提交 Issue 和 Pull Request 來改進這個功能。

### 開發環境設置

1. Fork 項目
2. 創建功能分支
3. 進行開發和測試
4. 提交 Pull Request

### 代碼規範

- 遵循項目現有的代碼風格
- 添加適當的註釋
- 編寫測試用例
- 更新文檔

## 許可證

本功能遵循 Open WebUI 項目的許可證。
