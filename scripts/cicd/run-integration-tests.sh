#!/bin/bash
set -e

echo "🔗 開始集成測試..."

# 創建報告目錄
mkdir -p reports

# 等待服務啟動
echo "⏳ 等待數據庫服務啟動..."
until pg_isready -h postgres -p 5432 -U $POSTGRES_USER; do
    echo "等待 PostgreSQL..."
    sleep 2
done

echo "⏳ 等待 Redis 服務啟動..."
until redis-cli -h redis ping; do
    echo "等待 Redis..."
    sleep 2
done

# 設置集成測試環境
export DATABASE_URL="************************************************************/$POSTGRES_DB"
export REDIS_URL="redis://redis:6379/0"
export WEBUI_SECRET_KEY="integration-test-secret"

# 啟動後端服務
echo "🚀 啟動後端服務..."
cd backend
source .venv/bin/activate

# 運行數據庫遷移
python -c "
from open_webui.main import app
from open_webui.config import DATABASE_URL
print(f'數據庫連接: {DATABASE_URL}')
"

# 在後台啟動應用
uvicorn open_webui.main:app --host 0.0.0.0 --port 8080 &
BACKEND_PID=$!

# 等待後端啟動
echo "⏳ 等待後端服務啟動..."
for i in {1..30}; do
    if curl -f http://localhost:8080/health > /dev/null 2>&1; then
        echo "✅ 後端服務已啟動"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ 後端服務啟動超時"
        kill $BACKEND_PID || true
        exit 1
    fi
    sleep 2
done

cd ..

# 運行 API 集成測試
echo "🔌 運行 API 集成測試..."
cat > test_integration.py << 'EOF'
import requests
import json
import sys

def test_health_endpoint():
    """測試健康檢查端點"""
    try:
        response = requests.get("http://localhost:8080/health")
        assert response.status_code == 200
        print("✅ 健康檢查端點測試通過")
        return True
    except Exception as e:
        print(f"❌ 健康檢查端點測試失敗: {e}")
        return False

def test_api_endpoints():
    """測試主要 API 端點"""
    endpoints = [
        "/api/v1/auths/signin",
        "/api/v1/models",
        "/api/v1/prompts",
    ]
    
    passed = 0
    for endpoint in endpoints:
        try:
            response = requests.get(f"http://localhost:8080{endpoint}")
            if response.status_code in [200, 401, 403]:  # 這些都是預期的響應
                print(f"✅ {endpoint} 端點可訪問")
                passed += 1
            else:
                print(f"⚠️ {endpoint} 端點返回狀態碼: {response.status_code}")
        except Exception as e:
            print(f"❌ {endpoint} 端點測試失敗: {e}")
    
    return passed == len(endpoints)

if __name__ == "__main__":
    tests_passed = 0
    total_tests = 2
    
    if test_health_endpoint():
        tests_passed += 1
    
    if test_api_endpoints():
        tests_passed += 1
    
    print(f"集成測試結果: {tests_passed}/{total_tests} 通過")
    
    if tests_passed == total_tests:
        sys.exit(0)
    else:
        sys.exit(1)
EOF

python test_integration.py

# 清理
kill $BACKEND_PID || true

# 生成集成測試報告
cat > reports/integration-tests.xml << EOF
<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite name="Integration Tests" tests="2" failures="0" time="$(date +%s)">
    <testcase name="Health Endpoint" classname="integration" time="0"/>
    <testcase name="API Endpoints" classname="integration" time="0"/>
  </testsuite>
</testsuites>
EOF

echo "✅ 集成測試完成"