#!/bin/bash
set -e

echo "🚀 開始部署到 Production 環境..."

# 設置變量
PRODUCTION_HOST="${PRODUCTION_HOST:-openwebui.example.com}"
IMAGE_NAME="${CI_REGISTRY_IMAGE:-open-webui}"
IMAGE_TAG="${CI_COMMIT_SHORT_SHA:-latest}"
NAMESPACE="${PRODUCTION_NAMESPACE:-production}"

# 部署前安全檢查
echo "🔒 部署前安全檢查..."

# 檢查是否為主分支
if [ "$CI_COMMIT_REF_NAME" != "main" ]; then
    echo "❌ 只能從 main 分支部署到生產環境"
    exit 1
fi

# 檢查必要的環境變量
REQUIRED_VARS=("PRODUCTION_WEBUI_SECRET_KEY" "PRODUCTION_DATABASE_URL")
for var in "${REQUIRED_VARS[@]}"; do
    if [ -z "${!var}" ]; then
        echo "❌ 缺少必要的環境變量: $var"
        exit 1
    fi
done

# 檢查鏡像安全掃描結果
if [ -f "reports/trivy-image.json" ]; then
    HIGH_VULNS=$(jq '[.Results[]?.Vulnerabilities[]? | select(.Severity == "HIGH" or .Severity == "CRITICAL")] | length' reports/trivy-image.json)
    if [ "$HIGH_VULNS" -gt 0 ]; then
        echo "⚠️ 發現 $HIGH_VULNS 個高危漏洞，是否繼續部署？"
        read -p "輸入 'yes' 繼續: " confirm
        if [ "$confirm" != "yes" ]; then
            echo "❌ 部署已取消"
            exit 1
        fi
    fi
fi

# 藍綠部署策略
echo "🔄 使用藍綠部署策略..."

if [ -n "$KUBECONFIG" ]; then
    echo "☸️ Kubernetes 藍綠部署..."
    
    # 創建命名空間
    kubectl create namespace $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -
    
    # 檢查當前活躍版本
    CURRENT_VERSION=$(kubectl get deployment open-webui -n $NAMESPACE -o jsonpath='{.metadata.labels.version}' 2>/dev/null || echo "none")
    
    if [ "$CURRENT_VERSION" = "blue" ]; then
        NEW_VERSION="green"
        OLD_VERSION="blue"
    else
        NEW_VERSION="blue"
        OLD_VERSION="green"
    fi
    
    echo "🔄 部署新版本: $NEW_VERSION"
    
    # 創建新版本部署清單
    cat > deployment-production-$NEW_VERSION.yaml << EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: open-webui-$NEW_VERSION
  namespace: $NAMESPACE
  labels:
    app: open-webui
    version: $NEW_VERSION
spec:
  replicas: 3
  selector:
    matchLabels:
      app: open-webui
      version: $NEW_VERSION
  template:
    metadata:
      labels:
        app: open-webui
        version: $NEW_VERSION
    spec:
      containers:
      - name: open-webui
        image: $IMAGE_NAME:$IMAGE_TAG
        ports:
        - containerPort: 8080
        env:
        - name: WEBUI_SECRET_KEY
          value: "$PRODUCTION_WEBUI_SECRET_KEY"
        - name: DATABASE_URL
          value: "$PRODUCTION_DATABASE_URL"
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
EOF

    # 部署新版本
    kubectl apply -f deployment-production-$NEW_VERSION.yaml
    
    # 等待新版本就緒
    echo "⏳ 等待新版本就緒..."
    kubectl rollout status deployment/open-webui-$NEW_VERSION -n $NAMESPACE --timeout=600s
    
    # 健康檢查
    echo "🏥 健康檢查..."
    kubectl wait --for=condition=ready pod -l app=open-webui,version=$NEW_VERSION -n $NAMESPACE --timeout=300s
    
    # 切換流量
    echo "🔀 切換流量到新版本..."
    kubectl patch service open-webui-service -n $NAMESPACE -p '{"spec":{"selector":{"version":"'$NEW_VERSION'"}}}'
    
    # 等待一段時間確保穩定
    echo "⏳ 等待服務穩定..."
    sleep 30
    
    # 最終健康檢查
    if curl -f "https://$PRODUCTION_HOST/health" > /dev/null 2>&1; then
        echo "✅ 新版本運行正常，清理舊版本..."
        kubectl delete deployment open-webui-$OLD_VERSION -n $NAMESPACE --ignore-not-found=true
    else
        echo "❌ 新版本健康檢查失敗，回滾..."
        kubectl patch service open-webui-service -n $NAMESPACE -p '{"spec":{"selector":{"version":"'$OLD_VERSION'"}}}'
        kubectl delete deployment open-webui-$NEW_VERSION -n $NAMESPACE
        exit 1
    fi

elif [ -n "$PRODUCTION_SSH_KEY" ]; then
    echo "🐳 Docker Compose 藍綠部署..."
    
    # 創建部署腳本
    cat > deploy_production.sh << EOF
#!/bin/bash
set -e

# 檢查當前運行的容器
CURRENT_CONTAINER=\$(docker ps --filter "name=open-webui-" --format "{{.Names}}" | head -1)

if [[ "\$CURRENT_CONTAINER" == *"blue"* ]]; then
    NEW_COLOR="green"
    OLD_COLOR="blue"
else
    NEW_COLOR="blue"
    OLD_COLOR="green"
fi

echo "🔄 部署新版本: \$NEW_COLOR"

# 拉取新鏡像
docker pull $IMAGE_NAME:$IMAGE_TAG

# 啟動新容器
docker run -d \\
    --name "open-webui-\$NEW_COLOR" \\
    --network production \\
    -e WEBUI_SECRET_KEY="$PRODUCTION_WEBUI_SECRET_KEY" \\
    -e DATABASE_URL="$PRODUCTION_DATABASE_URL" \\
    -v production_data:/app/backend/data \\
    $IMAGE_NAME:$IMAGE_TAG

# 等待新容器就緒
for i in {1..30}; do
    if docker exec "open-webui-\$NEW_COLOR" curl -f http://localhost:8080/health > /dev/null 2>&1; then
        echo "✅ 新容器就緒"
        break
    fi
    if [ \$i -eq 30 ]; then
        echo "❌ 新容器啟動失敗"
        docker rm -f "open-webui-\$NEW_COLOR"
        exit 1
    fi
    sleep 2
done

# 更新負載均衡器配置（假設使用 nginx）
cat > /etc/nginx/sites-available/openwebui << EOL
upstream openwebui {
    server open-webui-\$NEW_COLOR:8080;
}

server {
    listen 80;
    server_name $PRODUCTION_HOST;
    
    location / {
        proxy_pass http://openwebui;
        proxy_set_header Host \\\$host;
        proxy_set_header X-Real-IP \\\$remote_addr;
    }
}
EOL

# 重載 nginx
nginx -s reload

# 等待穩定
sleep 30

# 最終檢查
if curl -f http://localhost/health > /dev/null 2>&1; then
    echo "✅ 部署成功，清理舊容器..."
    docker rm -f "open-webui-\$OLD_COLOR" || true
else
    echo "❌ 部署失敗，回滾..."
    # 回滾配置
    cat > /etc/nginx/sites-available/openwebui << EOL
upstream openwebui {
    server open-webui-\$OLD_COLOR:8080;
}
EOL
    nginx -s reload
    docker rm -f "open-webui-\$NEW_COLOR"
    exit 1
fi

echo "🚀 生產部署完成"
EOF

    # 執行部署
    scp -i "$PRODUCTION_SSH_KEY" deploy_production.sh "$PRODUCTION_USER@$PRODUCTION_HOST:/tmp/"
    ssh -i "$PRODUCTION_SSH_KEY" "$PRODUCTION_USER@$PRODUCTION_HOST" "chmod +x /tmp/deploy_production.sh && /tmp/deploy_production.sh"
fi

# 部署後驗證
echo "🧪 部署後驗證..."
python smoke_test.py "https://$PRODUCTION_HOST"

# 性能測試
echo "⚡ 性能測試..."
cat > performance_test.py << 'EOF'
import requests
import time
import statistics

def performance_test(base_url, num_requests=10):
    """簡單的性能測試"""
    response_times = []
    
    for i in range(num_requests):
        start_time = time.time()
        try:
            response = requests.get(f"{base_url}/health", timeout=10)
            if response.status_code == 200:
                response_times.append(time.time() - start_time)
        except:
            pass
    
    if response_times:
        avg_time = statistics.mean(response_times)
        max_time = max(response_times)
        min_time = min(response_times)
        
        print(f"📊 性能測試結果:")
        print(f"   平均響應時間: {avg_time:.3f}s")
        print(f"   最大響應時間: {max_time:.3f}s") 
        print(f"   最小響應時間: {min_time:.3f}s")
        
        return avg_time < 1.0  # 期望平均響應時間小於1秒
    
    return False

if __name__ == "__main__":
    import sys
    base_url = sys.argv[1] if len(sys.argv) > 1 else "https://openwebui.example.com"
    success = performance_test(base_url)
    sys.exit(0 if success else 1)
EOF

python performance_test.py "https://$PRODUCTION_HOST"

# 記錄部署信息
cat > reports/production-deployment.json << EOF
{
  "environment": "production",
  "host": "$PRODUCTION_HOST",
  "image": "$IMAGE_NAME:$IMAGE_TAG",
  "commit": "$CI_COMMIT_SHORT_SHA",
  "deployed_at": "$(date -Iseconds)",
  "deployment_strategy": "blue-green",
  "health_check": "passed",
  "performance_test": "passed"
}
EOF

# 清理臨時文件
rm -f deploy_production.sh deployment-production-*.yaml smoke_test.py performance_test.py

echo "✅ Production 部署完成"
echo "🌐 訪問地址: https://$PRODUCTION_HOST"
echo "📊 查看部署報告: reports/production-deployment.json"