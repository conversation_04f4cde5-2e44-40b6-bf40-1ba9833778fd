#!/bin/bash
set -e

echo "🚀 開始 CI/CD 流程..."

# 設置環境
export CI_COMMIT_SHORT_SHA=${CI_COMMIT_SHORT_SHA:-$(git rev-parse --short HEAD)}
export CI_COMMIT_SHA=${CI_COMMIT_SHA:-$(git rev-parse HEAD)}
export CI_PROJECT_URL=${CI_PROJECT_URL:-"https://gitlab.com/example/open-webui"}

# 創建必要目錄
mkdir -p reports logs

# 記錄開始時間
START_TIME=$(date +%s)
echo "開始時間: $(date)"

# 執行 CI/CD 階段
case "${1:-all}" in
    "validate")
        validate-code.sh
        ;;
    "build")
        build-frontend.sh
        build-backend.sh
        ;;
    "test")
        run-unit-tests.sh
        run-integration-tests.sh
        ;;
    "security")
        run-security-scan.sh
        run-code-quality.sh
        ;;
    "package")
        build-docker-image.sh
        ;;
    "all")
        echo "🔄 執行完整 CI/CD 流程..."
        
        # 階段 1: 驗證
        echo "📋 階段 1: 代碼驗證"
        validate-code.sh
        
        # 階段 2: 構建
        echo "🏗️ 階段 2: 構建應用"
        build-frontend.sh
        build-backend.sh
        
        # 階段 3: 測試
        echo "🧪 階段 3: 運行測試"
        run-unit-tests.sh
        
        # 階段 4: 安全和質量
        echo "🔒 階段 4: 安全和質量檢查"
        run-security-scan.sh
        run-code-quality.sh
        
        # 階段 5: 打包
        echo "📦 階段 5: 構建鏡像"
        build-docker-image.sh
        ;;
    *)
        echo "用法: $0 [validate|build|test|security|package|all]"
        exit 1
        ;;
esac

# 計算執行時間
END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))

# 生成最終報告
cat > reports/cicd-summary.json << EOF
{
  "pipeline_id": "${CI_PIPELINE_ID:-local}",
  "commit": "$CI_COMMIT_SHORT_SHA",
  "branch": "${CI_COMMIT_REF_NAME:-$(git branch --show-current)}",
  "start_time": "$(date -d @$START_TIME -Iseconds)",
  "end_time": "$(date -d @$END_TIME -Iseconds)",
  "duration_seconds": $DURATION,
  "status": "success",
  "stages_completed": [
    "validate",
    "build", 
    "test",
    "security",
    "package"
  ]
}
EOF

echo "✅ CI/CD 流程完成"
echo "⏱️ 總執行時間: $((DURATION / 60))分$((DURATION % 60))秒"
echo "📊 查看詳細報告: reports/"