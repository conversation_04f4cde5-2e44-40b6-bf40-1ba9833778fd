#!/bin/bash
set -e

echo "🎭 開始端到端測試..."

# 創建報告目錄
mkdir -p reports cypress/screenshots cypress/videos

# 設置環境變量
export CYPRESS_baseUrl="http://localhost:3000"
export DATABASE_URL="************************************************************/$POSTGRES_DB"

# 啟動完整應用
echo "🚀 啟動完整應用..."
cd backend
source .venv/bin/activate

# 啟動後端
uvicorn open_webui.main:app --host 0.0.0.0 --port 8080 &
BACKEND_PID=$!

cd ..

# 啟動前端開發服務器
npm run dev -- --port 3000 &
FRONTEND_PID=$!

# 等待服務啟動
echo "⏳ 等待應用啟動..."
for i in {1..60}; do
    if curl -f http://localhost:3000 > /dev/null 2>&1 && curl -f http://localhost:8080/health > /dev/null 2>&1; then
        echo "✅ 應用已啟動"
        break
    fi
    if [ $i -eq 60 ]; then
        echo "❌ 應用啟動超時"
        kill $BACKEND_PID $FRONTEND_PID || true
        exit 1
    fi
    sleep 2
done

# 運行 Cypress 測試
echo "🧪 運行 Cypress E2E 測試..."
npx cypress run \
    --config video=true,screenshotOnRunFailure=true \
    --reporter junit \
    --reporter-options "mochaFile=reports/e2e-tests.xml" \
    --browser chromium || {
    echo "⚠️ 部分 E2E 測試失敗"
    TEST_FAILED=true
}

# 清理進程
kill $BACKEND_PID $FRONTEND_PID || true

# 生成測試摘要
TOTAL_TESTS=$(grep -o 'tests="[0-9]*"' reports/e2e-tests.xml | grep -o '[0-9]*' || echo "0")
FAILED_TESTS=$(grep -o 'failures="[0-9]*"' reports/e2e-tests.xml | grep -o '[0-9]*' || echo "0")

cat > reports/e2e-summary.json << EOF
{
  "total_tests": $TOTAL_TESTS,
  "failed_tests": $FAILED_TESTS,
  "success_rate": "$(( (TOTAL_TESTS - FAILED_TESTS) * 100 / TOTAL_TESTS ))%",
  "timestamp": "$(date -Iseconds)",
  "commit": "$CI_COMMIT_SHORT_SHA"
}
EOF

echo "✅ E2E 測試完成"
echo "📊 測試結果: $((TOTAL_TESTS - FAILED_TESTS))/$TOTAL_TESTS 通過"

if [ "$TEST_FAILED" = "true" ]; then
    exit 1
fi