#!/bin/bash
set -e

echo "📦 開始構建 Docker 鏡像..."

# 設置變量
IMAGE_NAME="${CI_REGISTRY_IMAGE:-open-webui}"
IMAGE_TAG="${CI_COMMIT_SHORT_SHA:-latest}"
BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ')

# 構建參數
BUILD_ARGS=(
    "--build-arg BUILD_HASH=$CI_COMMIT_SHORT_SHA"
    "--build-arg BUILD_DATE=$BUILD_DATE"
    "--label org.opencontainers.image.created=$BUILD_DATE"
    "--label org.opencontainers.image.revision=$CI_COMMIT_SHA"
    "--label org.opencontainers.image.source=$CI_PROJECT_URL"
)

# 多階段構建策略
echo "🏗️ 構建主鏡像..."
docker build \
    "${BUILD_ARGS[@]}" \
    -t "$IMAGE_NAME:$IMAGE_TAG" \
    -t "$IMAGE_NAME:latest" \
    -f Dockerfile \
    .

# 構建 CUDA 版本（如果需要）
if [ "$BUILD_CUDA" = "true" ]; then
    echo "🚀 構建 CUDA 鏡像..."
    docker build \
        "${BUILD_ARGS[@]}" \
        --build-arg USE_CUDA=true \
        -t "$IMAGE_NAME:$IMAGE_TAG-cuda" \
        -t "$IMAGE_NAME:cuda" \
        -f Dockerfile \
        .
fi

# 構建 Ollama 版本（如果需要）
if [ "$BUILD_OLLAMA" = "true" ]; then
    echo "🦙 構建 Ollama 鏡像..."
    docker build \
        "${BUILD_ARGS[@]}" \
        --build-arg USE_OLLAMA=true \
        -t "$IMAGE_NAME:$IMAGE_TAG-ollama" \
        -t "$IMAGE_NAME:ollama" \
        -f Dockerfile \
        .
fi

# 鏡像安全掃描
echo "🔍 掃描鏡像安全漏洞..."
trivy image --format json --output reports/trivy-image.json "$IMAGE_NAME:$IMAGE_TAG" || {
    echo "⚠️ 鏡像安全掃描發現問題"
}

# 鏡像大小分析
echo "📊 分析鏡像大小..."
IMAGE_SIZE=$(docker images "$IMAGE_NAME:$IMAGE_TAG" --format "table {{.Size}}" | tail -n 1)
LAYER_INFO=$(docker history "$IMAGE_NAME:$IMAGE_TAG" --format "table {{.CreatedBy}}\t{{.Size}}" --no-trunc)

cat > reports/image-analysis.json << EOF
{
  "image_name": "$IMAGE_NAME:$IMAGE_TAG",
  "image_size": "$IMAGE_SIZE",
  "build_date": "$BUILD_DATE",
  "commit": "$CI_COMMIT_SHORT_SHA",
  "layers": $(echo "$LAYER_INFO" | jq -R -s 'split("\n") | map(select(length > 0))')
}
EOF

# 推送鏡像到註冊表
if [ "$CI_REGISTRY" ]; then
    echo "📤 推送鏡像到註冊表..."
    
    # 推送主鏡像
    docker push "$IMAGE_NAME:$IMAGE_TAG"
    docker push "$IMAGE_NAME:latest"
    
    # 推送其他版本
    if [ "$BUILD_CUDA" = "true" ]; then
        docker push "$IMAGE_NAME:$IMAGE_TAG-cuda"
        docker push "$IMAGE_NAME:cuda"
    fi
    
    if [ "$BUILD_OLLAMA" = "true" ]; then
        docker push "$IMAGE_NAME:$IMAGE_TAG-ollama"
        docker push "$IMAGE_NAME:ollama"
    fi
    
    echo "✅ 鏡像推送完成"
else
    echo "ℹ️ 跳過鏡像推送（未配置註冊表）"
fi

# 生成部署清單
cat > deployment-manifest.yaml << EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: open-webui
  labels:
    app: open-webui
    version: "$IMAGE_TAG"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: open-webui
  template:
    metadata:
      labels:
        app: open-webui
        version: "$IMAGE_TAG"
    spec:
      containers:
      - name: open-webui
        image: "$IMAGE_NAME:$IMAGE_TAG"
        ports:
        - containerPort: 8080
        env:
        - name: WEBUI_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: open-webui-secrets
              key: secret-key
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: open-webui-service
spec:
  selector:
    app: open-webui
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: ClusterIP
EOF

echo "✅ Docker 鏡像構建完成"
echo "📊 鏡像大小: $IMAGE_SIZE"
echo "🏷️ 鏡像標籤: $IMAGE_NAME:$IMAGE_TAG"