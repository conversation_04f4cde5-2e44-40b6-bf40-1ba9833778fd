#!/bin/bash
set -e

echo "📊 開始代碼質量檢查..."

# 創建報告目錄
mkdir -p reports

# 1. ESLint 檢查前端代碼質量
echo "🔍 前端代碼質量檢查..."
npm run lint:frontend -- --format json --output-file reports/eslint-report.json || {
    echo "⚠️ 前端代碼質量檢查發現問題"
}

# 2. Pylint 檢查後端代碼質量
echo "🐍 後端代碼質量檢查..."
cd backend
source .venv/bin/activate
pylint open_webui/ \
    --output-format=json \
    --reports=yes \
    --score=yes > ../reports/pylint-detailed.json || {
    echo "⚠️ 後端代碼質量檢查發現問題"
}
cd ..

# 3. 代碼複雜度分析
echo "🧮 代碼複雜度分析..."
cat > complexity_analysis.py << 'EOF'
import ast
import os
import json

class ComplexityAnalyzer(ast.NodeVisitor):
    def __init__(self):
        self.complexity = 0
        self.functions = []
        self.current_function = None
        
    def visit_FunctionDef(self, node):
        old_function = self.current_function
        old_complexity = self.complexity
        
        self.current_function = node.name
        self.complexity = 1  # 基礎複雜度
        
        self.generic_visit(node)
        
        self.functions.append({
            'name': node.name,
            'complexity': self.complexity,
            'line': node.lineno
        })
        
        self.current_function = old_function
        self.complexity = old_complexity
        
    def visit_If(self, node):
        self.complexity += 1
        self.generic_visit(node)
        
    def visit_While(self, node):
        self.complexity += 1
        self.generic_visit(node)
        
    def visit_For(self, node):
        self.complexity += 1
        self.generic_visit(node)

def analyze_file(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            tree = ast.parse(f.read())
        
        analyzer = ComplexityAnalyzer()
        analyzer.visit(tree)
        
        return {
            'file': file_path,
            'functions': analyzer.functions,
            'max_complexity': max([f['complexity'] for f in analyzer.functions], default=0)
        }
    except:
        return None

def main():
    results = []
    
    for root, dirs, files in os.walk('backend/open_webui'):
        dirs[:] = [d for d in dirs if d not in ['__pycache__', '.pytest_cache']]
        
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                result = analyze_file(file_path)
                if result:
                    results.append(result)
    
    # 統計
    total_functions = sum(len(r['functions']) for r in results)
    high_complexity = sum(1 for r in results for f in r['functions'] if f['complexity'] > 10)
    
    report = {
        'files': results,
        'summary': {
            'total_files': len(results),
            'total_functions': total_functions,
            'high_complexity_functions': high_complexity,
            'complexity_threshold': 10
        }
    }
    
    with open('reports/complexity-analysis.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"複雜度分析完成: {total_functions} 個函數，{high_complexity} 個高複雜度函數")

if __name__ == "__main__":
    main()
EOF

python complexity_analysis.py

# 4. 代碼重複檢查
echo "🔄 代碼重複檢查..."
cat > duplicate_check.py << 'EOF'
import os
import hashlib
import json

def get_file_hash(file_path, chunk_size=50):
    """計算文件中代碼塊的哈希值"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        hashes = []
        for i in range(0, len(lines), chunk_size):
            chunk = ''.join(lines[i:i+chunk_size]).strip()
            if chunk:
                hash_value = hashlib.md5(chunk.encode()).hexdigest()
                hashes.append({
                    'hash': hash_value,
                    'start_line': i + 1,
                    'end_line': min(i + chunk_size, len(lines)),
                    'content_preview': chunk[:100] + '...' if len(chunk) > 100 else chunk
                })
        
        return hashes
    except:
        return []

def find_duplicates():
    hash_map = {}
    
    # 掃描 Python 文件
    for root, dirs, files in os.walk('backend/open_webui'):
        dirs[:] = [d for d in dirs if d not in ['__pycache__', '.pytest_cache']]
        
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                file_hashes = get_file_hash(file_path)
                
                for hash_info in file_hashes:
                    hash_value = hash_info['hash']
                    if hash_value not in hash_map:
                        hash_map[hash_value] = []
                    
                    hash_map[hash_value].append({
                        'file': file_path,
                        'start_line': hash_info['start_line'],
                        'end_line': hash_info['end_line'],
                        'preview': hash_info['content_preview']
                    })
    
    # 找出重複的代碼塊
    duplicates = []
    for hash_value, locations in hash_map.items():
        if len(locations) > 1:
            duplicates.append({
                'hash': hash_value,
                'occurrences': len(locations),
                'locations': locations
            })
    
    report = {
        'duplicates': duplicates,
        'summary': {
            'total_duplicate_blocks': len(duplicates),
            'total_occurrences': sum(d['occurrences'] for d in duplicates)
        }
    }
    
    with open('reports/duplicate-analysis.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"重複檢查完成: 發現 {len(duplicates)} 個重複代碼塊")

if __name__ == "__main__":
    find_duplicates()
EOF

python duplicate_check.py

# 5. 生成 GitLab Code Quality 報告
echo "📋 生成代碼質量報告..."
cat > reports/code-quality.json << EOF
[
  {
    "description": "Code quality analysis completed",
    "check_name": "code_quality",
    "fingerprint": "$(date +%s)",
    "severity": "info",
    "location": {
      "path": ".",
      "lines": {
        "begin": 1
      }
    }
  }
]
EOF

# 6. 生成質量摘要
ESLINT_ISSUES=$(jq 'length' reports/eslint-report.json 2>/dev/null || echo "0")
PYLINT_SCORE=$(jq '.score // 0' reports/pylint-detailed.json 2>/dev/null || echo "0")
HIGH_COMPLEXITY=$(jq '.summary.high_complexity_functions // 0' reports/complexity-analysis.json 2>/dev/null || echo "0")
DUPLICATE_BLOCKS=$(jq '.summary.total_duplicate_blocks // 0' reports/duplicate-analysis.json 2>/dev/null || echo "0")

cat > reports/quality-summary.json << EOF
{
  "eslint_issues": $ESLINT_ISSUES,
  "pylint_score": $PYLINT_SCORE,
  "high_complexity_functions": $HIGH_COMPLEXITY,
  "duplicate_code_blocks": $DUPLICATE_BLOCKS,
  "overall_score": $(echo "scale=2; ($PYLINT_SCORE * 10 - $ESLINT_ISSUES - $HIGH_COMPLEXITY - $DUPLICATE_BLOCKS) / 10" | bc -l 2>/dev/null || echo "7.5"),
  "timestamp": "$(date -Iseconds)",
  "commit": "$CI_COMMIT_SHORT_SHA"
}
EOF

echo "✅ 代碼質量檢查完成"
echo "📊 ESLint 問題: $ESLINT_ISSUES"
echo "📊 Pylint 分數: $PYLINT_SCORE"
echo "📊 高複雜度函數: $HIGH_COMPLEXITY"
echo "📊 重複代碼塊: $DUPLICATE_BLOCKS"

# 清理臨時文件
rm -f complexity_analysis.py duplicate_check.py