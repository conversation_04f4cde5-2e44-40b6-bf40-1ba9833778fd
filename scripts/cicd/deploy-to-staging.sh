#!/bin/bash
set -e

echo "🚀 開始部署到 Staging 環境..."

# 設置變量
STAGING_HOST="${STAGING_HOST:-staging.openwebui.example.com}"
IMAGE_NAME="${CI_REGISTRY_IMAGE:-open-webui}"
IMAGE_TAG="${CI_COMMIT_SHORT_SHA:-latest}"
NAMESPACE="${STAGING_NAMESPACE:-staging}"

# 部署前檢查
echo "🔍 部署前檢查..."
if [ -z "$KUBECONFIG" ] && [ -z "$STAGING_SSH_KEY" ]; then
    echo "❌ 未配置部署憑證（KUBECONFIG 或 STAGING_SSH_KEY）"
    exit 1
fi

# Kubernetes 部署
if [ -n "$KUBECONFIG" ]; then
    echo "☸️ 使用 Kubernetes 部署..."
    
    # 創建命名空間（如果不存在）
    kubectl create namespace $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -
    
    # 更新部署清單
    sed -i "s|image: .*|image: $IMAGE_NAME:$IMAGE_TAG|g" deployment-manifest.yaml
    
    # 應用配置
    kubectl apply -f deployment-manifest.yaml -n $NAMESPACE
    
    # 等待部署完成
    echo "⏳ 等待部署完成..."
    kubectl rollout status deployment/open-webui -n $NAMESPACE --timeout=300s
    
    # 獲取服務端點
    SERVICE_IP=$(kubectl get service open-webui-service -n $NAMESPACE -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "pending")
    
    echo "✅ Kubernetes 部署完成"
    echo "🌐 服務端點: $SERVICE_IP"

# Docker Compose 部署
elif [ -n "$STAGING_SSH_KEY" ]; then
    echo "🐳 使用 Docker Compose 部署..."
    
    # 創建臨時部署腳本
    cat > deploy_staging.sh << EOF
#!/bin/bash
set -e

# 停止現有服務
docker-compose -f docker-compose.staging.yml down || true

# 拉取最新鏡像
docker pull $IMAGE_NAME:$IMAGE_TAG

# 更新環境變量
export WEBUI_DOCKER_TAG=$IMAGE_TAG
export OPEN_WEBUI_PORT=3000

# 啟動服務
docker-compose -f docker-compose.staging.yml up -d

# 等待服務啟動
for i in {1..30}; do
    if curl -f http://localhost:3000/health > /dev/null 2>&1; then
        echo "✅ 服務啟動成功"
        break
    fi
    if [ \$i -eq 30 ]; then
        echo "❌ 服務啟動超時"
        exit 1
    fi
    sleep 2
done

echo "🚀 Staging 部署完成"
EOF

    # 創建 staging docker-compose 文件
    cat > docker-compose.staging.yml << EOF
version: '3.8'

services:
  open-webui:
    image: $IMAGE_NAME:$IMAGE_TAG
    container_name: open-webui-staging
    ports:
      - "3000:8080"
    environment:
      - WEBUI_SECRET_KEY=\${WEBUI_SECRET_KEY}
      - DATABASE_URL=\${DATABASE_URL}
      - OPENAI_API_KEY=\${OPENAI_API_KEY}
    volumes:
      - staging_data:/app/backend/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  staging_data:
EOF

    # 通過 SSH 部署
    scp -i "$STAGING_SSH_KEY" deploy_staging.sh docker-compose.staging.yml "$STAGING_USER@$STAGING_HOST:/tmp/"
    ssh -i "$STAGING_SSH_KEY" "$STAGING_USER@$STAGING_HOST" "chmod +x /tmp/deploy_staging.sh && /tmp/deploy_staging.sh"
    
    echo "✅ Docker Compose 部署完成"
fi

# 部署後測試
echo "🧪 部署後測試..."
HEALTH_URL="https://$STAGING_HOST/health"

for i in {1..10}; do
    if curl -f "$HEALTH_URL" > /dev/null 2>&1; then
        echo "✅ 健康檢查通過"
        break
    fi
    if [ $i -eq 10 ]; then
        echo "❌ 健康檢查失敗"
        exit 1
    fi
    sleep 10
done

# 煙霧測試
echo "💨 運行煙霧測試..."
cat > smoke_test.py << 'EOF'
import requests
import sys

def smoke_test(base_url):
    """運行基本的煙霧測試"""
    tests = [
        {"name": "健康檢查", "url": f"{base_url}/health", "expected": 200},
        {"name": "主頁", "url": f"{base_url}/", "expected": 200},
        {"name": "API 端點", "url": f"{base_url}/api/v1/models", "expected": [200, 401]},
    ]
    
    passed = 0
    for test in tests:
        try:
            response = requests.get(test["url"], timeout=10)
            expected = test["expected"]
            if isinstance(expected, list):
                success = response.status_code in expected
            else:
                success = response.status_code == expected
                
            if success:
                print(f"✅ {test['name']}: {response.status_code}")
                passed += 1
            else:
                print(f"❌ {test['name']}: {response.status_code} (期望: {expected})")
        except Exception as e:
            print(f"❌ {test['name']}: 錯誤 - {e}")
    
    return passed == len(tests)

if __name__ == "__main__":
    base_url = sys.argv[1] if len(sys.argv) > 1 else "https://staging.openwebui.example.com"
    success = smoke_test(base_url)
    sys.exit(0 if success else 1)
EOF

python smoke_test.py "https://$STAGING_HOST"

# 記錄部署信息
cat > reports/staging-deployment.json << EOF
{
  "environment": "staging",
  "host": "$STAGING_HOST",
  "image": "$IMAGE_NAME:$IMAGE_TAG",
  "commit": "$CI_COMMIT_SHORT_SHA",
  "deployed_at": "$(date -Iseconds)",
  "deployment_method": "${KUBECONFIG:+kubernetes}${STAGING_SSH_KEY:+docker-compose}",
  "health_check": "passed",
  "smoke_test": "passed"
}
EOF

# 清理臨時文件
rm -f deploy_staging.sh docker-compose.staging.yml smoke_test.py

echo "✅ Staging 部署完成"
echo "🌐 訪問地址: https://$STAGING_HOST"