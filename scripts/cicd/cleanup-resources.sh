#!/bin/bash
set -e

echo "🧹 開始清理 CI/CD 資源..."

# 清理 Docker 資源
echo "🐳 清理 Docker 資源..."

# 清理構建緩存
docker builder prune -f || true

# 清理未使用的鏡像
docker image prune -f || true

# 清理未使用的容器
docker container prune -f || true

# 清理未使用的網絡
docker network prune -f || true

# 清理未使用的卷（謹慎）
if [ "$CLEANUP_VOLUMES" = "true" ]; then
    docker volume prune -f || true
fi

# 清理測試數據庫
echo "🗄️ 清理測試數據庫..."
if [ -f "test.db" ]; then
    rm -f test.db
fi

if [ -f "backend/test.db" ]; then
    rm -f backend/test.db
fi

# 清理臨時文件
echo "📁 清理臨時文件..."
find . -name "*.pyc" -delete || true
find . -name "__pycache__" -type d -exec rm -rf {} + || true
find . -name ".pytest_cache" -type d -exec rm -rf {} + || true
find . -name "*.log" -delete || true

# 清理 Node.js 緩存
if [ -d ".npm" ]; then
    rm -rf .npm
fi

# 清理 Python 虛擬環境（如果是臨時的）
if [ "$CLEANUP_VENV" = "true" ] && [ -d "backend/.venv" ]; then
    rm -rf backend/.venv
fi

# 清理 Cypress 緩存
if [ -d "cypress/downloads" ]; then
    rm -rf cypress/downloads
fi

# 清理舊的報告文件（保留最近的）
echo "📊 清理舊報告..."
if [ -d "reports" ]; then
    find reports -name "*.xml" -mtime +7 -delete || true
    find reports -name "*.json" -mtime +7 -delete || true
fi

# 清理舊的構建產物
echo "🏗️ 清理構建產物..."
if [ -d "build" ] && [ "$KEEP_BUILD" != "true" ]; then
    rm -rf build
fi

if [ -d "dist" ] && [ "$KEEP_DIST" != "true" ]; then
    rm -rf dist
fi

# 清理 Kubernetes 臨時資源
if [ -n "$KUBECONFIG" ]; then
    echo "☸️ 清理 Kubernetes 臨時資源..."
    
    # 清理失敗的 Pod
    kubectl delete pods --field-selector=status.phase=Failed --all-namespaces || true
    
    # 清理已完成的 Job
    kubectl delete jobs --field-selector=status.conditions[0].type=Complete --all-namespaces || true
fi

# 清理 GitLab CI 緩存（如果超過大小限制）
if [ -n "$CI_PROJECT_ID" ]; then
    echo "🦊 檢查 GitLab CI 緩存..."
    
    # 這裡可以添加清理 GitLab CI 緩存的邏輯
    # 例如：清理超過 1GB 的緩存
fi

# 清理舊的 Docker 鏡像（保留最近的幾個版本）
echo "🏷️ 清理舊的 Docker 鏡像..."
if [ -n "$CI_REGISTRY_IMAGE" ]; then
    # 獲取所有標籤，保留最近的 5 個
    IMAGES_TO_DELETE=$(docker images "$CI_REGISTRY_IMAGE" --format "table {{.Repository}}:{{.Tag}}\t{{.CreatedAt}}" | \
                       tail -n +2 | \
                       sort -k2 -r | \
                       tail -n +6 | \
                       awk '{print $1}')
    
    for image in $IMAGES_TO_DELETE; do
        echo "🗑️ 刪除舊鏡像: $image"
        docker rmi "$image" || true
    done
fi

# 生成清理報告
echo "📋 生成清理報告..."
DISK_USAGE_BEFORE=${DISK_USAGE_BEFORE:-0}
DISK_USAGE_AFTER=$(df . | tail -1 | awk '{print $3}')
SPACE_FREED=$((DISK_USAGE_BEFORE - DISK_USAGE_AFTER))

cat > reports/cleanup-report.json << EOF
{
  "cleanup_time": "$(date -Iseconds)",
  "disk_usage_before_kb": $DISK_USAGE_BEFORE,
  "disk_usage_after_kb": $DISK_USAGE_AFTER,
  "space_freed_kb": $SPACE_FREED,
  "space_freed_mb": $((SPACE_FREED / 1024)),
  "actions_performed": [
    "docker_cleanup",
    "temp_files_cleanup",
    "cache_cleanup",
    "old_reports_cleanup",
    "build_artifacts_cleanup"
  ]
}
EOF

# 顯示清理統計
echo "✅ 清理完成"
echo "💾 釋放空間: $((SPACE_FREED / 1024)) MB"

# 最終的磁盤使用情況
echo "📊 當前磁盤使用情況:"
df -h .