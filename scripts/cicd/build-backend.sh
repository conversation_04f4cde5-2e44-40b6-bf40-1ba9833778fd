#!/bin/bash
set -e

echo "🏗️ 開始構建後端..."

# 進入後端目錄
cd backend

# 創建虛擬環境
echo "🐍 創建 Python 虛擬環境..."
python3 -m venv .venv
source .venv/bin/activate

# 升級 pip 和安裝 uv
pip install --upgrade pip uv

# 安裝依賴
echo "📦 安裝後端依賴..."
uv pip install -r requirements.txt

# 編譯 Python 文件
echo "🔨 編譯 Python 文件..."
python -m compileall open_webui/

# 運行基本語法檢查
echo "🔍 語法檢查..."
python -m py_compile open_webui/main.py

# 檢查關鍵模組是否可以導入
echo "📋 檢查模組導入..."
python -c "
import sys
sys.path.append('.')
try:
    from open_webui.main import app
    print('✅ 主應用模組導入成功')
except ImportError as e:
    print(f'❌ 主應用模組導入失敗: {e}')
    sys.exit(1)
"

# 創建分發目錄
mkdir -p dist
cp -r open_webui dist/
cp requirements.txt dist/
cp start.sh dist/

cd ..

# 生成構建報告
cat > reports/backend-build.json << EOF
{
  "status": "success",
  "python_version": "$(python3 --version)",
  "timestamp": "$(date -Iseconds)",
  "commit": "$CI_COMMIT_SHORT_SHA"
}
EOF

echo "✅ 後端構建完成"