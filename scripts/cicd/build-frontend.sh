#!/bin/bash
set -e

echo "🏗️ 開始構建前端..."

# 安裝依賴
echo "📦 安裝前端依賴..."
npm ci --force

# 運行 pyodide 準備腳本
echo "🐍 準備 Pyodide..."
npm run pyodide:fetch

# 構建前端
echo "🔨 構建前端應用..."
npm run build

# 檢查構建產物
if [ ! -d "build" ]; then
    echo "❌ 前端構建失敗：build 目錄不存在"
    exit 1
fi

# 計算構建產物大小
BUILD_SIZE=$(du -sh build | cut -f1)
echo "📊 前端構建產物大小: $BUILD_SIZE"

# 生成構建報告
cat > reports/frontend-build.json << EOF
{
  "status": "success",
  "build_size": "$BUILD_SIZE",
  "timestamp": "$(date -Iseconds)",
  "commit": "$CI_COMMIT_SHORT_SHA"
}
EOF

echo "✅ 前端構建完成"