#!/usr/bin/env python3
"""
測試筆記資料夾功能的簡單腳本
運行前請確保 Open WebUI 後端服務正在運行
"""

import requests
import json
import sys

# 配置
BASE_URL = "http://localhost:8080/api/v1"
TEST_USER_TOKEN = "your_test_token_here"  # 請替換為實際的測試用戶 token

def test_folder_crud():
    """測試資料夾的 CRUD 操作"""
    headers = {
        "Authorization": f"Bearer {TEST_USER_TOKEN}",
        "Content-Type": "application/json"
    }
    
    print("🧪 測試資料夾 CRUD 操作...")
    
    # 1. 創建根資料夾
    print("1. 創建根資料夾...")
    folder_data = {
        "name": "測試資料夾",
        "description": "這是一個測試資料夾",
        "color": "#3b82f6"
    }
    
    response = requests.post(f"{BASE_URL}/notes/folders/", json=folder_data, headers=headers)
    if response.status_code == 200:
        root_folder = response.json()
        print(f"✅ 根資料夾創建成功: {root_folder['name']} (ID: {root_folder['id']})")
    else:
        print(f"❌ 根資料夾創建失敗: {response.status_code} - {response.text}")
        return None
    
    # 2. 創建子資料夾
    print("2. 創建子資料夾...")
    subfolder_data = {
        "name": "子資料夾",
        "description": "這是一個子資料夾",
        "color": "#10b981",
        "parent_id": root_folder['id']
    }
    
    response = requests.post(f"{BASE_URL}/notes/folders/", json=subfolder_data, headers=headers)
    if response.status_code == 200:
        subfolder = response.json()
        print(f"✅ 子資料夾創建成功: {subfolder['name']} (ID: {subfolder['id']})")
    else:
        print(f"❌ 子資料夾創建失敗: {response.status_code} - {response.text}")
        return None
    
    # 3. 獲取資料夾樹
    print("3. 獲取資料夾樹...")
    response = requests.get(f"{BASE_URL}/notes/folders/tree", headers=headers)
    if response.status_code == 200:
        tree = response.json()
        print(f"✅ 資料夾樹獲取成功，共 {len(tree)} 個根資料夾")
        for folder in tree:
            print(f"   📁 {folder['name']} (筆記數: {folder['note_count']}, 總筆記數: {folder['total_note_count']})")
            for child in folder.get('children', []):
                print(f"     📁 {child['name']} (筆記數: {child['note_count']})")
    else:
        print(f"❌ 資料夾樹獲取失敗: {response.status_code} - {response.text}")
    
    # 4. 更新資料夾
    print("4. 更新資料夾...")
    update_data = {
        "name": "更新後的測試資料夾",
        "description": "這是更新後的描述",
        "color": "#ef4444"
    }
    
    response = requests.put(f"{BASE_URL}/notes/folders/{root_folder['id']}", json=update_data, headers=headers)
    if response.status_code == 200:
        updated_folder = response.json()
        print(f"✅ 資料夾更新成功: {updated_folder['name']}")
    else:
        print(f"❌ 資料夾更新失敗: {response.status_code} - {response.text}")
    
    # 5. 獲取資料夾路徑
    print("5. 獲取子資料夾路徑...")
    response = requests.get(f"{BASE_URL}/notes/folders/{subfolder['id']}/path", headers=headers)
    if response.status_code == 200:
        path = response.json()
        path_str = " > ".join([f['name'] for f in path])
        print(f"✅ 資料夾路徑: {path_str}")
    else:
        print(f"❌ 資料夾路徑獲取失敗: {response.status_code} - {response.text}")
    
    return root_folder, subfolder

def test_note_folder_integration(root_folder, subfolder):
    """測試筆記與資料夾的集成"""
    headers = {
        "Authorization": f"Bearer {TEST_USER_TOKEN}",
        "Content-Type": "application/json"
    }
    
    print("\n🧪 測試筆記與資料夾集成...")
    
    # 1. 在根資料夾中創建筆記
    print("1. 在根資料夾中創建筆記...")
    note_data = {
        "title": "根資料夾中的筆記",
        "data": {
            "content": {
                "md": "這是在根資料夾中的測試筆記",
                "html": "<p>這是在根資料夾中的測試筆記</p>"
            }
        },
        "folder_id": root_folder['id']
    }
    
    response = requests.post(f"{BASE_URL}/notes/create", json=note_data, headers=headers)
    if response.status_code == 200:
        note = response.json()
        print(f"✅ 筆記創建成功: {note['title']} (ID: {note['id']})")
    else:
        print(f"❌ 筆記創建失敗: {response.status_code} - {response.text}")
        return
    
    # 2. 獲取資料夾中的筆記
    print("2. 獲取根資料夾中的筆記...")
    response = requests.get(f"{BASE_URL}/notes/folders/{root_folder['id']}/notes", headers=headers)
    if response.status_code == 200:
        notes = response.json()
        print(f"✅ 根資料夾中有 {len(notes)} 個筆記")
        for note in notes:
            print(f"   📝 {note['title']}")
    else:
        print(f"❌ 獲取資料夾筆記失敗: {response.status_code} - {response.text}")
    
    # 3. 移動筆記到子資料夾
    print("3. 移動筆記到子資料夾...")
    move_data = {
        "folder_id": subfolder['id']
    }
    
    response = requests.post(f"{BASE_URL}/notes/{note['id']}/move", json=move_data, headers=headers)
    if response.status_code == 200:
        moved_note = response.json()
        print(f"✅ 筆記移動成功: {moved_note['title']} -> 子資料夾")
    else:
        print(f"❌ 筆記移動失敗: {response.status_code} - {response.text}")
    
    # 4. 驗證筆記已移動
    print("4. 驗證筆記已移動到子資料夾...")
    response = requests.get(f"{BASE_URL}/notes/folders/{subfolder['id']}/notes", headers=headers)
    if response.status_code == 200:
        notes = response.json()
        print(f"✅ 子資料夾中現在有 {len(notes)} 個筆記")
    else:
        print(f"❌ 驗證失敗: {response.status_code} - {response.text}")

def cleanup_test_data(root_folder, subfolder):
    """清理測試數據"""
    headers = {
        "Authorization": f"Bearer {TEST_USER_TOKEN}",
        "Content-Type": "application/json"
    }
    
    print("\n🧹 清理測試數據...")
    
    # 刪除子資料夾（會先移動其中的筆記）
    response = requests.delete(f"{BASE_URL}/notes/folders/{subfolder['id']}", headers=headers)
    if response.status_code == 200:
        print("✅ 子資料夾刪除成功")
    else:
        print(f"❌ 子資料夾刪除失敗: {response.status_code} - {response.text}")
    
    # 刪除根資料夾
    response = requests.delete(f"{BASE_URL}/notes/folders/{root_folder['id']}", headers=headers)
    if response.status_code == 200:
        print("✅ 根資料夾刪除成功")
    else:
        print(f"❌ 根資料夾刪除失敗: {response.status_code} - {response.text}")

def main():
    """主測試函數"""
    print("🚀 開始測試 Open WebUI 筆記資料夾功能\n")
    
    if TEST_USER_TOKEN == "your_test_token_here":
        print("❌ 請先設置有效的測試用戶 token")
        sys.exit(1)
    
    try:
        # 測試資料夾 CRUD
        folders = test_folder_crud()
        if not folders:
            print("❌ 資料夾 CRUD 測試失敗，停止測試")
            return
        
        root_folder, subfolder = folders
        
        # 測試筆記與資料夾集成
        test_note_folder_integration(root_folder, subfolder)
        
        # 清理測試數據
        cleanup_test_data(root_folder, subfolder)
        
        print("\n🎉 所有測試完成！")
        
    except requests.exceptions.ConnectionError:
        print("❌ 無法連接到 Open WebUI 後端服務，請確保服務正在運行")
    except Exception as e:
        print(f"❌ 測試過程中發生錯誤: {e}")

if __name__ == "__main__":
    main()
