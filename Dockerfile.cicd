# 單一容器 CI/CD Dockerfile
# 包含構建、測試和部署所需的所有工具
FROM node:22-alpine3.20 AS base

# 安裝系統依賴和工具
RUN apk add --no-cache \
    git \
    curl \
    bash \
    jq \
    docker \
    docker-compose \
    python3 \
    python3-dev \
    py3-pip \
    gcc \
    musl-dev \
    libffi-dev \
    openssl-dev \
    build-base \
    chromium \
    chromium-chromedriver \
    # 安全掃描工具
    trivy \
    # 代碼質量工具
    sonar-scanner-cli

# 設置工作目錄
WORKDIR /workspace

# 安裝 Python 工具
RUN pip3 install --no-cache-dir \
    black \
    pylint \
    pytest \
    pytest-cov \
    bandit \
    safety \
    uv

# 安裝 Node.js 全局工具
RUN npm install -g \
    eslint \
    prettier \
    @typescript-eslint/parser \
    @typescript-eslint/eslint-plugin \
    cypress \
    vitest \
    audit-ci

# 設置 Playwright 環境變量
ENV PLAYWRIGHT_BROWSERS_PATH=/usr/bin
ENV PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=1

# 創建腳本目錄
RUN mkdir -p /scripts

# 複製 CI/CD 腳本
COPY scripts/cicd/ /scripts/
RUN chmod +x /scripts/*.sh

# 設置環境變量
ENV NODE_ENV=production
ENV PYTHONPATH=/workspace/backend
ENV PATH="/scripts:$PATH"

# 健康檢查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# 默認命令
CMD ["/scripts/run-cicd.sh"]