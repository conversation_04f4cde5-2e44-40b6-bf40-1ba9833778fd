<script lang="ts">
	import { toast } from 'svelte-sonner';
	import { getContext, onMount, createEventDispatcher } from 'svelte';
	import { getLicenseKeys, setLicenseKey, getMachineFingerprint } from '../../../apis/licenseKey';
	import type { LicenseKeys } from '$lib/types';
	const i18n = getContext('i18n');

	let licenseKeyInput: string = '';
	let currentLicenseKey: LicenseKeys = [];
	let isLoading: boolean = true;
	let machineFingerprint: string = '';
	let isMachineFingerprintLoading: boolean = true;

	const dispatch = createEventDispatcher();

	const copyToClipboard = async (text: string) => {
		try {
			await navigator.clipboard.writeText(text);
			toast.success($i18n.t('licenseKey.copiedToClipboard'));
		} catch (err) {
			console.error('Failed to copy: ', err);
		}
	};

	onMount(async () => {
		await fetchLicenseKey();
		await fetchMachineFingerprint();
	});

	const fetchMachineFingerprint = async () => {
		isMachineFingerprintLoading = true;
		try {
			const token = localStorage.token;
			if (!token) {
				toast.error($i18n.t('errors.authenticationRequired'));
				return;
			}
			const response = await getMachineFingerprint(token);
			console.log('res', response)
			if (response) {
				machineFingerprint = response;
			} else {
				machineFingerprint = '';
			}
		} catch (error: any) {
			console.error('Error fetching machine fingerprint:', error);
			const errorMessage = error.message || $i18n.t('licenseKey.errorFetchingMachineInfo');
			toast.error(errorMessage);
			machineFingerprint = '';
		} finally {
			isMachineFingerprintLoading = false;
		}
	};

	const fetchLicenseKey = async () => {
		isLoading = true;
		try {
			const token = localStorage.token;
			if (!token) {
				toast.error($i18n.t('errors.authenticationRequired'));
				currentLicenseKey = $i18n.t('Authentication failed, please login again');
				return;
			}
			const response = await getLicenseKeys(token);
			console.log('response', response);
			if (response && response.length) {
				currentLicenseKey = response;
			} else {
				currentLicenseKey = [];
			}
		} catch (error: any) {
			console.error('Error fetching license key:', error);
			const errorMessage = error.message || $i18n.t('licenseKey.errorFetchingBase');
			toast.error(errorMessage);
			currentLicenseKey = [];
		} finally {
			isLoading = false;
		}
	};

	const handleSubmit = async () => {
		if (!licenseKeyInput.trim()) {
			toast.error($i18n.t('licenseKey.cannotBeEmpty'));
			return;
		}

		try {
			const token = localStorage.token;
			if (!token) {
				toast.error($i18n.t('errors.authenticationRequired'));
				return;
			}
			await setLicenseKey(token, licenseKeyInput.trim());
			toast.success($i18n.t('licenseKey.savedSuccess'));
			licenseKeyInput = '';
			await fetchLicenseKey();
		} catch (error: any) {
			toast.error(error.message || $i18n.t('licenseKey.saveError'));
			console.error('Error saving license key:', error);
		}
	};
</script>

<div class="space-y-6 p-1.5">
	<div>
		<h3 class="text-base font-semibold leading-7 text-gray-900 dark:text-white">
			{$i18n.t('licenseKey.configurationTitle')}
		</h3>
		<p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
			{$i18n.t('licenseKey.configurationDescription')}
		</p>
	</div>
	<div class="space-y-3">
		<h4 class="text-md font-semibold leading-7 text-gray-900 dark:text-white">
			{$i18n.t('licenseKey.machineFingerprint')}
		</h4>
		{#if isMachineFingerprintLoading}
			<p class="mt-2 text-sm text-gray-500 dark:text-gray-400">{$i18n.t('Loading...')}</p>
		{:else}
			<div class="mt-2 overflow-x-auto">
				<table class="w-full table-fixed divide-y divide-gray-200 dark:divide-gray-700">
					<tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
						{#if machineFingerprint}
								<tr>
									<td
										class="w-full px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700"
										on:click={() => copyToClipboard(machineFingerprint)}
										title="Copy Machine Fingerprint"
									>
										{machineFingerprint}
									</td>
								</tr>
						{:else}
							<tr>
								<td
									colspan="2"
									class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"
								>
									{$i18n.t('licenseKey.noMachineInfoFound')}
								</td>
							</tr>
						{/if}
					</tbody>
				</table>
			</div>
		{/if}
	</div>

	<!-- Input Form -->
	<div class="space-y-3">
		<div>
			<label
				for="license-key-input"
				class="block text-sm font-medium text-gray-700 dark:text-gray-300"
			>
				{$i18n.t('licenseKey.inputLabel')}
			</label>
			<div class="mt-1">
				<input
					type="text"
					id="license-key-input"
					bind:value={licenseKeyInput}
					class="block w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm placeholder-gray-400 dark:placeholder-gray-500"
					placeholder={$i18n.t('licenseKey.inputPlaceholder')}
				/>
			</div>
		</div>
	</div>

	<div class="flex justify-end pt-3">
		<button
			type="button"
			on:click={handleSubmit}
			class="px-3.5 py-2 text-sm font-semibold text-white bg-gray-800 hover:bg-gray-900 dark:bg-gray-200 dark:text-black dark:hover:bg-gray-300 rounded-md shadow-sm transition duration-150 ease-in-out"
		>
			{$i18n.t('Save')}
		</button>
	</div>

	<!-- Display Current License Key Table -->
	<div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
		<h4 class="text-md font-semibold leading-7 text-gray-900 dark:text-white">
			{$i18n.t('licenseKey.currentKeyTitle')}
		</h4>
		{#if isLoading}
			<p class="mt-2 text-sm text-gray-500 dark:text-gray-400">{$i18n.t('Loading...')}</p>
		{:else}
			<div class="mt-2 overflow-x-auto">
				<table class="w-full table-fixed divide-y divide-gray-200 dark:divide-gray-700">
					<thead class="bg-gray-50 dark:bg-gray-700">
						<tr class="flex">
							<th
								scope="col"
								class="w-[90%] px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
							>
								{$i18n.t('licenseKey.keyLabel')}
							</th>
							<th
								scope="col"
								class="w-[20%] px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
							>
								{$i18n.t('licenseKey.expiryDate')}
							</th>
						</tr>
					</thead>
					<tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
						{#if isLoading}
							<tr class="flex">
								<td
									colspan="2"
									class=" px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"
								>
									{$i18n.t('Loading...')}
								</td>
							</tr>
						{:else if currentLicenseKey.length > 0}
							{#each currentLicenseKey as licenseKey}
								<tr class="flex overflow-hidden">
									<td
										class="w-[90%] text-sm text-gray-900 dark:text-white overflow-hidden"
									>
										<button 
											type="button"
											class="flex px-6 py-4 w-full text-left font-mono whitespace-nowrap cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 px-2 py-1 rounded transition-colors"
											on:click={() => {
												navigator.clipboard.writeText(licenseKey.license_key);
												toast.success($i18n.t('licenseKey.copiedToClipboard'));
											}}
										>
											{licenseKey.license_key}
										</button>
									</td>
									<td
										class="w-[20%] px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"
									>
										<code>{licenseKey.expires_at || $i18n.t('licenseKey.NoExpiryDate')}</code>
									</td>
								</tr>
							{/each}
						{:else}
							<tr class="flex w-full">
								<td
									colspan="2"
									class="w-full px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"
								>
									{$i18n.t('licenseKey.noKeyFound')}
								</td>
							</tr>
						{/if}
					</tbody>
				</table>
			</div>
			<div class="mt-4 flex justify-start">
				<button
					type="button"
					on:click={fetchLicenseKey}
					class="px-3.5 py-2 text-sm font-semibold text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm transition duration-150 ease-in-out"
				>
					{$i18n.t('refresh')}
				</button>
			</div>
		{/if}
	</div>
</div>
