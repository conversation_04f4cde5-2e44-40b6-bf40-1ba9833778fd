import { WEBUI_API_BASE_URL } from '$lib/constants';
import type { LicenseKeys } from '$lib/types';

interface LicenseKeyPayload {
	license_key: string;
}

// 取得授權碼
export const getLicenseKeys = async (token: string = '') => {
	let error = null;

	const res = await fetch(`${WEBUI_API_BASE_URL}/configs/license_keys`, {
		method: 'GET',
		headers: {
			Accept: 'application/json',
			'Content-Type': 'application/json',
			authorization: `Bearer ${token}`
		}
	})
		.then(async (res) => {
			if (!res) throw new Error('Failed to get license key');
			return await res.json()
		})
		.catch((err) => {
			error = err.detail || err.message || 'Failed to get license key';
			console.error(`Error getting license key: ${error}`, err);
			return { license_keys: [], error: error };
		});
	if (error) {
		throw new Error(error);
	}
	return res as LicenseKeys;
};

// 設定授權碼
export const setLicenseKey = async (token: string, licenseKey: string) => {
	let error = null;

	const payload: LicenseKeyPayload = { license_key: licenseKey };

	const res = await fetch(`${WEBUI_API_BASE_URL}/configs/license_key`, {
		method: 'POST',
		headers: {
			Accept: 'application/json',
			'Content-Type': 'application/json',
			authorization: `Bearer ${token}`
		},
		body: JSON.stringify(payload)
	})
		.then(async (res) => {
			if (!res.ok) throw await res.json();
			return res.json();
		})
		.catch((err) => {
			error = err.detail || err.message || 'Failed to set license key';
			console.error(`Error setting license key: ${error}`, err);
			return null;
		});

	if (error) {
		throw new Error(error);
	}

	return res;
};

// 取得機器資訊
export const getMachineFingerprint = async (token: string = '') => {
	let error = null;

	const res = await fetch(`${WEBUI_API_BASE_URL}/configs/machine_fingerprint`, {
		method: 'GET',
		headers: {
			Accept: 'application/json',
			'Content-Type': 'application/json',
			authorization: `Bearer ${token}`
		}
	})
		.then(async (res) => {
			if (!res) throw new Error('Failed to get machine fingerprint');
			return await res.json()
		})
		.catch((err) => {
			error = err.detail || err.message || 'Failed to get machine fingerprint';
			console.error(`Error getting machine fingerprint: ${error}`, err);
			return { machine_fingerprint: {}, error: error };
		});
	if (error) {
		throw new Error(error);
	}
	return res.hardware_fingerprint;
};
