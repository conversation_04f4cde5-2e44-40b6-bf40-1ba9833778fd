<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>笔记访问测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .test-title {
            font-weight: bold;
            color: #555;
            margin-bottom: 10px;
        }
        .test-link {
            display: inline-block;
            padding: 10px 15px;
            margin: 5px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background-color 0.3s;
        }
        .test-link:hover {
            background-color: #0056b3;
        }
        .test-link.invalid {
            background-color: #dc3545;
        }
        .test-link.invalid:hover {
            background-color: #c82333;
        }
        .description {
            font-size: 14px;
            color: #666;
            margin-top: 10px;
        }
        .instructions {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 笔记访问测试页面</h1>
        
        <div class="instructions">
            <strong>测试说明：</strong>
            <ul>
                <li>点击下面的链接测试不同的笔记访问场景</li>
                <li>观察浏览器控制台的日志输出</li>
                <li>检查错误提示是否正确显示</li>
                <li>确认重定向行为是否符合预期</li>
            </ul>
        </div>

        <div class="test-section">
            <div class="test-title">1. 无效ID测试</div>
            <a href="http://localhost:5174/notes/" class="test-link invalid">空ID</a>
            <a href="http://localhost:5174/notes/   " class="test-link invalid">空白ID</a>
            <a href="http://localhost:5174/notes/invalid-id-123" class="test-link invalid">不存在的ID</a>
            <div class="description">
                这些链接应该显示相应的错误信息并重定向到首页
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">2. 权限测试</div>
            <a href="http://localhost:5174/notes/test-note-1" class="test-link">测试笔记1</a>
            <a href="http://localhost:5174/notes/test-note-2" class="test-link">测试笔记2</a>
            <div class="description">
                如果笔记存在但无权限访问，应显示权限错误信息
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">3. 正常访问测试</div>
            <a href="http://localhost:5174/notes" class="test-link">返回笔记列表</a>
            <div class="description">
                从笔记列表中选择一个有效的笔记ID进行测试
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">4. 调试信息</div>
            <div class="description">
                <strong>检查控制台日志：</strong>
                <ul>
                    <li><code>Loading note with ID: [id]</code> - 开始加载笔记</li>
                    <li><code>Note loaded successfully: [data]</code> - 笔记加载成功</li>
                    <li><code>Note not found or access denied for ID: [id]</code> - 笔记不存在或无权限</li>
                    <li><code>Error loading note: [error]</code> - 加载错误</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 添加一些JavaScript来增强测试体验
        document.addEventListener('DOMContentLoaded', function() {
            console.log('笔记访问测试页面已加载');
            console.log('请点击上面的链接进行测试，并观察控制台输出');
        });
    </script>
</body>
</html>
