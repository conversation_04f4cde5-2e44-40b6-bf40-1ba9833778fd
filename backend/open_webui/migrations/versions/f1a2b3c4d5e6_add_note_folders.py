"""Add note folders support

Revision ID: add_note_folders
Revises: 9f0c9cd09105
Create Date: 2025-01-01 00:00:00.000000

"""

from alembic import op
import sqlalchemy as sa

revision = "f1a2b3c4d5e6"
down_revision = "5d464ac8de32"
branch_labels = None
depends_on = None


def upgrade():
    # 創建 note_folder 表
    op.create_table(
        "note_folder",
        sa.Column("id", sa.String(), nullable=False, primary_key=True),
        sa.Column("user_id", sa.String(), nullable=False),
        sa.Column("name", sa.String(255), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("color", sa.String(7), nullable=True),
        sa.Column("parent_id", sa.String(), nullable=True),
        sa.Column("sort_order", sa.Integer(), nullable=True, default=0),
        sa.Column("access_control", sa.<PERSON>(), nullable=True),
        sa.Column("created_at", sa.<PERSON>nteger(), nullable=False),
        sa.Column("updated_at", sa.Big<PERSON>nteger(), nullable=False),
    )
    
    # 添加外鍵約束
    op.create_foreign_key(
        "fk_note_folder_parent",
        "note_folder",
        "note_folder",
        ["parent_id"],
        ["id"],
        ondelete="CASCADE"
    )
    
    # 創建索引
    op.create_index(
        "idx_note_folder_user_parent",
        "note_folder",
        ["user_id", "parent_id"]
    )
    
    op.create_index(
        "idx_note_folder_user_name",
        "note_folder",
        ["user_id", "name"]
    )
    
    op.create_index(
        "idx_note_folder_user_id",
        "note_folder",
        ["user_id"]
    )
    
    op.create_index(
        "idx_note_folder_parent_id",
        "note_folder",
        ["parent_id"]
    )
    
    # 檢查 folder_id 字段是否已存在，如果不存在才添加
    conn = op.get_bind()
    inspector = sa.inspect(conn)
    note_columns = [col['name'] for col in inspector.get_columns('note')]
    
    if 'folder_id' not in note_columns:
        # 在 note 表中添加 folder_id 字段
        op.add_column("note", sa.Column("folder_id", sa.String(), nullable=True))
    
    # 添加外鍵約束（如果不存在）
    try:
        op.create_foreign_key(
            "fk_note_folder",
            "note",
            "note_folder",
            ["folder_id"],
            ["id"],
            ondelete="SET NULL"
        )
    except Exception:
        # 外鍵約束可能已存在，忽略錯誤
        pass
    
    # 創建索引（如果不存在）
    try:
        op.create_index(
            "idx_note_user_folder",
            "note",
            ["user_id", "folder_id"]
        )
    except Exception:
        # 索引可能已存在，忽略錯誤
        pass
    
    try:
        op.create_index(
            "idx_note_folder_id",
            "note",
            ["folder_id"]
        )
    except Exception:
        # 索引可能已存在，忽略錯誤
        pass


def downgrade():
    # 刪除索引（如果存在）
    try:
        op.drop_index("idx_note_folder_id", "note")
    except Exception:
        pass
    try:
        op.drop_index("idx_note_user_folder", "note")
    except Exception:
        pass
    
    # 刪除外鍵約束（如果存在）
    try:
        op.drop_constraint("fk_note_folder", "note", type_="foreignkey")
    except Exception:
        pass
    
    # 刪除 note 表中的 folder_id 字段（如果存在）
    try:
        op.drop_column("note", "folder_id")
    except Exception:
        pass
    
    # 刪除 note_folder 表的索引（如果存在）
    try:
        op.drop_index("idx_note_folder_parent_id", "note_folder")
    except Exception:
        pass
    try:
        op.drop_index("idx_note_folder_user_id", "note_folder")
    except Exception:
        pass
    try:
        op.drop_index("idx_note_folder_user_name", "note_folder")
    except Exception:
        pass
    try:
        op.drop_index("idx_note_folder_user_parent", "note_folder")
    except Exception:
        pass
    
    # 刪除外鍵約束（如果存在）
    try:
        op.drop_constraint("fk_note_folder_parent", "note_folder", type_="foreignkey")
    except Exception:
        pass
    
    # 刪除 note_folder 表（如果存在）
    try:
        op.drop_table("note_folder")
    except Exception:
        pass
