import json
import base64
from datetime import datetime
from open_webui.utils.license.secure_key_manager import SecureKeyManager
from open_webui.utils.license.machine_data_collector import MachineDataCollector


class LicenseValidator:
    """授權碼驗證器，用於驗證授權碼的有效性"""
    PUBLIC_KEY_PATH = "public.pem"
    LICENSE_PREFIX = "LICKEY-"
    
    def __init__(self):
        """
        初始化授權碼驗證器
        """
        pass
    
    def validate_license(self, license_string):
        """
        驗證授權碼是否有效
        
        Returns:
            tuple: (bool, str, str) - 授權碼是否有效, 授權碼到期時間, 錯誤訊息
        """
        try:
            license_data = self.from_string(license_string)
            license_info = json.loads(license_data["license"])
            
            skm = SecureKeyManager(public_key_path=self.PUBLIC_KEY_PATH)
            
            payload = license_data["license"]
            signature = license_data["signature"]
            
            if not skm.verify(payload.encode(), signature):
                return False, "", "invalid signature"
            
            machine_now = MachineDataCollector.get_hardware_fingerprint()
            if license_info["machine"] != machine_now:
                return False, "", "machine fingerprint not match"
            
            expiry_date = license_info["expiry"]
            if datetime.today().date() > datetime.strptime(expiry_date, "%Y-%m-%d").date():
                return False, expiry_date, "license expired"
            
            return True, expiry_date, ""
        except Exception:
            return False, "", "fail to validate license"
    
    def from_string(self, license_string):
        """
        將加密字串轉換回 JSON 格式
        
        Args:
            license_string: 加密的授權碼字串
            
        Returns:
            dict: 解密後的授權碼 JSON 物件
        """
        if not license_string.startswith(self.LICENSE_PREFIX):
            raise ValueError("無效的授權碼格式")
        
        encoded_part = license_string[len(self.LICENSE_PREFIX):]
        
        json_str = base64.b64decode(encoded_part).decode()
        
        return json.loads(json_str)