import base64
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import padding, rsa
from cryptography.hazmat.primitives.asymmetric.utils import Prehashed
import os

class SecureKeyManager:
    def __init__(self, public_key_path=None):
        self.public_key = None

        if public_key_path and os.path.exists(public_key_path):
            with open(public_key_path, "rb") as key_file:
                self.public_key = serialization.load_pem_public_key(key_file.read())

    def verify(self, data: bytes, signature: str) -> bool:
        try:
            # 使用相同的方式計算哈希值
            digest = hashes.Hash(hashes.SHA256())
            digest.update(data)
            data_hash = digest.finalize()
            
            self.public_key.verify(
                base64.b64decode(signature),
                data_hash,
                padding.PKCS1v15(),
                Prehashed(hashes.SHA256())
            )
            return True
        except Exception as e:
            print(f"驗證失敗: {e}")
            return False