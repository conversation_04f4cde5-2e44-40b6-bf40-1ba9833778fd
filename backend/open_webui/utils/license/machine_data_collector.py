import platform
import socket
import uuid
import hashlib
import subprocess
import os

class MachineDataCollector:
    @staticmethod
    def get_hardware_fingerprint() -> str:
        data = [
            platform.node(),
            platform.system(),
            platform.machine(),
            platform.processor(),
            MachineDataCollector.get_mac_address(),
            MachineDataCollector.get_cpu_id(),
            MachineDataCollector.get_disk_serial()
        ]
        combined = "|".join(data)
        return hashlib.sha256(combined.encode()).hexdigest()

    @staticmethod
    def get_mac_address() -> str:
        mac = uuid.getnode()
        return ":".join([f"{(mac >> ele) & 0xff:02x}" for ele in range(0, 8*6, 8)][::-1])

    @staticmethod
    def get_cpu_id() -> str:
        try:
            if platform.system() == "Windows":
                return subprocess.check_output("wmic cpu get ProcessorId", shell=True).decode().split("\n")[1].strip()
            elif platform.system() == "Linux":
                return subprocess.check_output("cat /proc/cpuinfo | grep Serial", shell=True).decode().split(":")[1].strip()
            elif platform.system() == "Darwin":
                return platform.processor()
        except:
            return "unknown"

    @staticmethod
    def get_disk_serial() -> str:
        try:
            if platform.system() == "Windows":
                return subprocess.check_output("wmic diskdrive get SerialNumber", shell=True).decode().split("\n")[1].strip()
            elif platform.system() == "Linux":
                return subprocess.check_output("lsblk -o SERIAL", shell=True).decode().split("\n")[1].strip()
            elif platform.system() == "Darwin":
                return subprocess.check_output(["system_profiler", "SPSerialATADataType"]).decode()
        except:
            return "unknown"