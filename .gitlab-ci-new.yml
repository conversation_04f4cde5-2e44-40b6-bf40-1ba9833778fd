# GitLab CI/CD 配置 - 單一容器解決方案
# 適用於 Open WebUI 項目的完整 CI/CD 流程

variables:
  # Docker 配置
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"
  DOCKER_HOST: tcp://docker:2376
  DOCKER_TLS_VERIFY: 1
  
  # 應用配置
  APP_NAME: "open-webui"
  REGISTRY_URL: "$CI_REGISTRY"
  IMAGE_TAG: "$CI_COMMIT_SHORT_SHA"
  
  # 緩存配置
  NODE_MODULES_CACHE: "node_modules"
  PIP_CACHE_DIR: "/cache/pip"
  
  # 測試配置
  POSTGRES_DB: "test_db"
  POSTGRES_USER: "test_user"
  POSTGRES_PASSWORD: "test_password"

# 定義階段
stages:
  - validate
  - build
  - test
  - security
  - package
  - deploy
  - cleanup

# 全局配置
default:
  image: 
    name: $CI_REGISTRY_IMAGE/cicd:latest
    pull_policy: if-not-present
  before_script:
    - echo "開始 CI/CD 流程 - Commit: $CI_COMMIT_SHORT_SHA"
    - echo "分支: $CI_COMMIT_REF_NAME"
    - echo "Pipeline ID: $CI_PIPELINE_ID"

# 緩存配置
.cache_config: &cache_config
  cache:
    key: 
      files:
        - package-lock.json
        - backend/requirements.txt
    paths:
      - node_modules/
      - .npm/
      - backend/.venv/
      - $PIP_CACHE_DIR
    policy: pull-push

# 僅在特定分支運行
.branch_rules: &branch_rules
  only:
    - main
    - develop
    - staging
    - /^feature\/.*$/
    - /^hotfix\/.*$/
    - /^release\/.*$/

# ==================== 驗證階段 ====================
code_validation:
  stage: validate
  <<: *cache_config
  <<: *branch_rules
  script:
    - echo "🔍 代碼驗證階段"
    - validate-code.sh
  artifacts:
    reports:
      junit: reports/validation-report.xml
    paths:
      - reports/
    expire_in: 1 day
  allow_failure: false

# ==================== 構建階段 ====================
build_frontend:
  stage: build
  <<: *cache_config
  <<: *branch_rules
  script:
    - echo "🏗️ 構建前端"
    - build-frontend.sh
  artifacts:
    paths:
      - build/
      - dist/
    expire_in: 1 day
  needs: ["code_validation"]

build_backend:
  stage: build
  <<: *cache_config
  <<: *branch_rules
  script:
    - echo "🏗️ 構建後端"
    - build-backend.sh
  artifacts:
    paths:
      - backend/dist/
    expire_in: 1 day
  needs: ["code_validation"]

# ==================== 測試階段 ====================
unit_tests:
  stage: test
  <<: *cache_config
  <<: *branch_rules
  services:
    - postgres:13-alpine
  variables:
    POSTGRES_DB: $POSTGRES_DB
    POSTGRES_USER: $POSTGRES_USER
    POSTGRES_PASSWORD: $POSTGRES_PASSWORD
  script:
    - echo "🧪 單元測試"
    - run-unit-tests.sh
  artifacts:
    reports:
      junit: reports/unit-tests.xml
      coverage_report:
        coverage_format: cobertura
        path: reports/coverage.xml
    paths:
      - reports/
      - coverage/
    expire_in: 1 week
  coverage: '/TOTAL.*\s+(\d+%)$/'
  needs: ["build_frontend", "build_backend"]

integration_tests:
  stage: test
  <<: *cache_config
  <<: *branch_rules
  services:
    - postgres:13-alpine
    - redis:6-alpine
  script:
    - echo "🔗 集成測試"
    - run-integration-tests.sh
  artifacts:
    reports:
      junit: reports/integration-tests.xml
    paths:
      - reports/
    expire_in: 1 week
  needs: ["build_frontend", "build_backend"]

e2e_tests:
  stage: test
  <<: *cache_config
  <<: *branch_rules
  services:
    - postgres:13-alpine
  script:
    - echo "🎭 端到端測試"
    - run-e2e-tests.sh
  artifacts:
    reports:
      junit: reports/e2e-tests.xml
    paths:
      - cypress/screenshots/
      - cypress/videos/
      - reports/
    expire_in: 1 week
    when: always
  needs: ["build_frontend", "build_backend"]

# ==================== 安全掃描階段 ====================
security_scan:
  stage: security
  <<: *branch_rules
  script:
    - echo "🔒 安全掃描"
    - run-security-scan.sh
  artifacts:
    reports:
      sast: reports/security-sast.json
      dependency_scanning: reports/security-deps.json
    paths:
      - reports/
    expire_in: 1 week
  needs: ["unit_tests"]
  allow_failure: true

code_quality:
  stage: security
  <<: *branch_rules
  script:
    - echo "📊 代碼質量檢查"
    - run-code-quality.sh
  artifacts:
    reports:
      codequality: reports/code-quality.json
    paths:
      - reports/
    expire_in: 1 week
  needs: ["unit_tests"]
  allow_failure: true

# ==================== 打包階段 ====================
build_docker_image:
  stage: package
  <<: *branch_rules
  services:
    - docker:24-dind
  variables:
    DOCKER_TLS_CERTDIR: "/certs"
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  script:
    - echo "📦 構建 Docker 鏡像"
    - build-docker-image.sh
  after_script:
    - docker logout $CI_REGISTRY
  needs: ["integration_tests", "e2e_tests", "security_scan"]
  only:
    - main
    - develop
    - staging

# ==================== 部署階段 ====================
deploy_staging:
  stage: deploy
  <<: *branch_rules
  environment:
    name: staging
    url: https://staging.openwebui.example.com
  script:
    - echo "🚀 部署到 Staging 環境"
    - deploy-to-staging.sh
  needs: ["build_docker_image"]
  only:
    - develop
    - staging
  when: manual

deploy_production:
  stage: deploy
  <<: *branch_rules
  environment:
    name: production
    url: https://openwebui.example.com
  script:
    - echo "🚀 部署到 Production 環境"
    - deploy-to-production.sh
  needs: ["build_docker_image"]
  only:
    - main
  when: manual
  allow_failure: false

# ==================== 清理階段 ====================
cleanup:
  stage: cleanup
  script:
    - echo "🧹 清理臨時資源"
    - cleanup-resources.sh
  when: always
  allow_failure: true

# ==================== 通知和報告 ====================
notify_success:
  stage: .post
  script:
    - echo "✅ CI/CD 流程成功完成"
    - send-notification.sh "success"
  when: on_success
  allow_failure: true

notify_failure:
  stage: .post
  script:
    - echo "❌ CI/CD 流程失敗"
    - send-notification.sh "failure"
  when: on_failure
  allow_failure: true